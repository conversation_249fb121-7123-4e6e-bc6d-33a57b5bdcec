2025-08-04 13:35:56,986 | INFO | ================================================================================
2025-08-04 13:35:56,987 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 13:35:56,989 | INFO | Session ID: migration_1754328956
2025-08-04 13:35:56,990 | INFO | Connection Type: fmcapi
2025-08-04 13:35:56,992 | INFO | 🔍 Connection Diagnostic:
2025-08-04 13:35:56,993 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 13:35:56,995 | INFO |    • fmcapi Available: True
2025-08-04 13:35:56,997 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 13:35:58,989 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 13:35:58,996 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 13:35:58,998 | INFO | 🔍 Connection diagnostic complete
2025-08-04 13:35:59,000 | INFO | ================================================================================
2025-08-04 13:35:59,005 | INFO | 🔍 Running pre-migration validation...
2025-08-04 13:36:01,205 | INFO | ================================================================================
2025-08-04 13:36:01,206 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 13:36:01,208 | INFO | ================================================================================
2025-08-04 13:36:01,210 | INFO | [OK] Overall Status: PASSED
2025-08-04 13:36:01,211 | INFO | [OK] Configuration: 1122 objects found
2025-08-04 13:36:01,213 | INFO |    • host_objects: 629
2025-08-04 13:36:01,214 | INFO |    • network_objects: 63
2025-08-04 13:36:01,215 | INFO |    • service_objects: 29
2025-08-04 13:36:01,217 | INFO |    • object_groups: 111
2025-08-04 13:36:01,218 | INFO |    • service_groups: 66
2025-08-04 13:36:01,219 | INFO |    • access_rules: 224
2025-08-04 13:36:01,221 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 13:36:01,222 | INFO | ================================================================================
2025-08-04 14:23:29,696 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_hosts.json
2025-08-04 14:23:34,100 | ERROR | Creation failed for TeleMedVT3: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:23:38,417 | ERROR | Creation failed for TelemedVT4: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:23:42,707 | ERROR | Creation failed for TelemedVT5: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:23:47,056 | ERROR | Creation failed for TeleMedVT1: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:23:51,376 | ERROR | Creation failed for Medent.VPN.net: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:13,994 | ERROR | Creation failed for Olympus.Inside.New: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:18,382 | ERROR | Creation failed for speculator: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:22,708 | ERROR | Creation failed for GEserviceNET: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:27,092 | ERROR | Creation failed for Mill.PACS.NET: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:31,485 | ERROR | Creation failed for DI.NET: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:35,896 | ERROR | Creation failed for STUDENT_VLAN: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:44,779 | ERROR | Creation failed for iPEOPLEremote: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:24:53,848 | ERROR | Creation failed for RALSplusLAN: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:25:43,567 | ERROR | Creation failed for MilleniumPACSnat-*************: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:25:47,898 | ERROR | Creation failed for obj-*************-*************: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:26:59,951 | ERROR | Creation failed for CHC.OPTUM.NAT.INTERNAL.SUB: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:04,236 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE1: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:08,503 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE2: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:12,902 | ERROR | Creation failed for BARRACUDA.CLOUD.EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:30,778 | ERROR | Creation failed for CLEARWATER3: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:35,136 | ERROR | Creation failed for CLEARWATER4: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:39,491 | ERROR | Creation failed for backblazeb2.com: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:43,868 | ERROR | Creation failed for HANYS.EXTERNAL.1: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:48,199 | ERROR | Creation failed for HANYS.INTERNAL.2: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:27:52,444 | ERROR | Creation failed for HANYS.EXTERNAL.3: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:28:10,322 | ERROR | Creation failed for MICROSOFTSTREAM.COM: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:28:10,358 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_networks.json
2025-08-04 14:28:14,609 | ERROR | Creation failed for obj-tcp-eq-80: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:28:18,823 | ERROR | Update failed for obj-tcp-eq-15002: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:28:23,154 | ERROR | Update failed for obj-tcp-eq-15331: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:28:27,406 | ERROR | Update failed for obj-tcp-eq-3389: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:28:31,632 | ERROR | Update failed for obj-tcp-eq-2222: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:28:44,332 | ERROR | Creation failed for obj-tcp-eq-23: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:28:52,820 | ERROR | Creation failed for obj-tcp-eq-5631: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:29:01,316 | ERROR | Creation failed for obj-udp-eq-5632: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:29:05,511 | ERROR | Creation failed for obj-tcp-eq-25: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:29:09,763 | ERROR | Creation failed for obj-tcp-eq-443: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:29:39,517 | ERROR | Creation failed for obj-tcp-eq-21: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:13,381 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_services.json
2025-08-04 14:30:17,701 | ERROR | Creation failed for Medivators: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:22,043 | ERROR | Creation failed for NUVODIA.INTERNAL.GROUP.NEW: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:26,422 | ERROR | Creation failed for NUVODIA.INTERNAL.PEER.NET1: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:30,714 | ERROR | Creation failed for DM_INLINE_NETWORK_4: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:35,118 | ERROR | Creation failed for DM_INLINE_NETWORK_6: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:39,457 | ERROR | Creation failed for FoodService: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:43,809 | ERROR | Creation failed for DI.Net.Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:48,112 | ERROR | Creation failed for STRATEGICSOLUTIONS.EXTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:52,429 | ERROR | Creation failed for Cardinal: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:30:56,716 | ERROR | Creation failed for medinotes: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:01,003 | ERROR | Creation failed for CitrixServers.dmz: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:05,345 | ERROR | Creation failed for MilleniumPACS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:09,667 | ERROR | Creation failed for ExchangeServers: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:13,982 | ERROR | Creation failed for TeleMedVT: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:18,302 | ERROR | Creation failed for PacsServers: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:22,671 | ERROR | Creation failed for MDI.OUT.Allow: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:26,996 | ERROR | Creation failed for eRXdataCenters: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:31,321 | ERROR | Creation failed for Medent.Interface: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:35,738 | ERROR | Creation failed for SMHA.RAD: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:40,157 | ERROR | Creation failed for RAD.PACS.READ: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:44,503 | ERROR | Creation failed for SOPHOS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:48,891 | ERROR | Creation failed for CitrixServers1: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:53,352 | ERROR | Creation failed for CitrixServers1_ref: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:31:57,716 | ERROR | Creation failed for MVO_Allow_OUTBOUND_Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:02,065 | ERROR | Creation failed for ProvationServers: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:06,481 | ERROR | Creation failed for AAI.NYOH.PACS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:10,813 | ERROR | Creation failed for Dolby_OUT: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:15,157 | ERROR | Creation failed for Dolby_Servers: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:19,468 | ERROR | Creation failed for Healthtouch.out: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:23,876 | ERROR | Creation failed for FoodSVC: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:28,150 | ERROR | Creation failed for ALBANYPACS.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:32,421 | ERROR | Creation failed for HIXNY: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:36,766 | ERROR | Creation failed for Olympus.inside.group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:41,041 | ERROR | Creation failed for MDI_Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:45,404 | ERROR | Creation failed for Brian_DHCP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:49,679 | ERROR | Creation failed for Schumacher.Inside: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:54,094 | ERROR | Creation failed for MEDENTHQ: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:32:58,417 | ERROR | Creation failed for MEDENT_GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:02,806 | ERROR | Creation failed for WINDOWS_XP_DENY: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:07,125 | ERROR | Creation failed for APPLE.OUT: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:11,409 | ERROR | Creation failed for ProviderOrg.External: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:15,754 | ERROR | Creation failed for CITRIX_EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:20,077 | ERROR | Creation failed for CITRIXGATEWAY.DMZ: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:24,457 | ERROR | Creation failed for CITRIX_INTERNAL_TO_DMZ: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:28,753 | ERROR | Creation failed for MIS_TEST: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:33,038 | ERROR | Creation failed for MARKETO_SPAMMER: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:37,344 | ERROR | Creation failed for ENDOWORKS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:41,678 | ERROR | Creation failed for CE2000.INTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:45,979 | ERROR | Creation failed for CE2000.EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:50,363 | ERROR | Creation failed for Group_24.97.36.3: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:54,750 | ERROR | Creation failed for Group_65.114.41.136: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:33:59,082 | ERROR | Creation failed for Group_12.39.198.49: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:03,376 | ERROR | Creation failed for Group_173.84.224.94: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:07,714 | ERROR | Creation failed for Group_12.152.123.2: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:12,119 | ERROR | Creation failed for HIXNY.MBMS.INTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:16,591 | ERROR | Creation failed for NUVODIA_VPN_NLH_PEER: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:20,948 | ERROR | Creation failed for CLEARWATERTEST: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:25,370 | ERROR | Creation failed for HIXNY.INTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:29,696 | ERROR | Creation failed for MDI.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:34,056 | ERROR | Creation failed for NYOH.INTERNAL.NET: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:38,424 | ERROR | Creation failed for NUVODIA.VPN.SENDPOINT.MASTER: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:42,716 | ERROR | Creation failed for DM_INLINE_NETWORK_7: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:47,018 | ERROR | Creation failed for DM_INLINE_NETWORK_8: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:51,329 | ERROR | Creation failed for DM_INLINE_NETWORK_9: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:34:55,755 | ERROR | Creation failed for SMHA.RAD.NEW: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:00,074 | ERROR | Creation failed for DM_INLINE_NETWORK_10: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:04,374 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:08,721 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:13,116 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:17,421 | ERROR | Creation failed for TCPUDP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:21,824 | ERROR | Creation failed for EXPANSE_VLANS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:26,257 | ERROR | Creation failed for SmartNet_Devices: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:30,600 | ERROR | Creation failed for Domain.Controllers.Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:34,977 | ERROR | Creation failed for Quest.NLH2Quest.Internal: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:39,356 | ERROR | Creation failed for CHANGE.HEALTHCARE.EXTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:43,678 | ERROR | Creation failed for CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:47,981 | ERROR | Creation failed for CHC.EXTERNAL.NETWORK: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:52,307 | ERROR | Creation failed for PHINMS.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:35:56,698 | ERROR | Creation failed for NLI.INTERNAL.NAT.CHC: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:01,063 | ERROR | Creation failed for NLI-BG-GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:05,386 | ERROR | Creation failed for DM_INLINE_NETWORK_11: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:09,731 | ERROR | Creation failed for QUEST.VPN.INTERNAL.GROUP.2: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:14,081 | ERROR | Creation failed for WEBSSO.MEDITECH.COM.EXTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:18,402 | ERROR | Creation failed for BACKLINE.LDAP.NLH.INTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:22,696 | ERROR | Creation failed for FIRECALLSYSTEM: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:27,122 | ERROR | Creation failed for FIRECALLSYSTEM__ENDPOINTS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:31,383 | ERROR | Creation failed for IT_DEPT: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:35,692 | ERROR | Creation failed for FULL_PORT_ACCESS: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:40,056 | ERROR | Creation failed for CISCO_INTERNAL_2_EXTERNAL_ACL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:44,340 | ERROR | Creation failed for HEALTHTOUCH.NLH.INTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:48,661 | ERROR | Creation failed for HEALTHTOUCH.PEER.INTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:53,028 | ERROR | Creation failed for Greycastle_Testing_External: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:36:57,408 | ERROR | Creation failed for LINKBG.SPAM.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:01,696 | ERROR | Creation failed for love.explorethebest.com.spam.group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:05,974 | ERROR | Creation failed for NLH.ACRONIS.GROUP.INSIDE: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:10,290 | ERROR | Creation failed for NLH.ACRONIS.GROUP.EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:14,587 | ERROR | Creation failed for BARRACUDA.LDAP.EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:18,912 | ERROR | Creation failed for REYHEALTH.EXTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:23,285 | ERROR | Creation failed for EMAIL.BLACKLIST.EXTERNAL: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:27,673 | ERROR | Creation failed for NLH.DI.GEDEVICES: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:32,008 | ERROR | Creation failed for DM_INLINE_NETWORK_3: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:36,397 | ERROR | Creation failed for blackblazeb2.goup: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:40,797 | ERROR | Creation failed for HAYNS.EXTERNAL.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:45,126 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:49,442 | ERROR | Creation failed for Incident.External: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:53,779 | ERROR | Creation failed for NLH.Firewall.Internal.Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:37:58,097 | ERROR | Creation failed for Clearwater.Internal.Group: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:38:02,379 | ERROR | Creation failed for QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:38:06,673 | ERROR | Creation failed for SSI.EXTERNAL.PEER.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:38:11,002 | ERROR | Creation failed for BANDWIDTH.TEST.GROUP.OUTSIDE: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:38:15,382 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.GROUP: fmcapi post() failed - no ID returned. Result: None
2025-08-04 14:38:15,429 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_object_groups.json
2025-08-04 14:38:19,585 | ERROR | Creation failed for PaceGlobalgrp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:23,731 | ERROR | Creation failed for timeservice: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:27,832 | ERROR | Creation failed for timeserviceUDP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:31,963 | ERROR | Creation failed for QUEST: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:36,059 | ERROR | Creation failed for citrixXML: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:40,197 | ERROR | Creation failed for GatewayDMZ: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:44,283 | ERROR | Creation failed for RSA: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:48,372 | ERROR | Creation failed for HFMBoces: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:52,501 | ERROR | Creation failed for GEinbound: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:56,679 | ERROR | Creation failed for GEoutbound: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:00,798 | ERROR | Creation failed for PetLinks: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:04,942 | ERROR | Creation failed for TeleVideoTcpUdp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:09,060 | ERROR | Creation failed for GEPACS: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:13,188 | ERROR | Creation failed for ExchangePorts: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:17,303 | ERROR | Creation failed for PrintPorts: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:21,419 | ERROR | Creation failed for PrinterPorts: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:25,484 | ERROR | Creation failed for IPSEC_ISAKMP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:29,612 | ERROR | Creation failed for EmdeonPorts: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:33,747 | ERROR | Creation failed for in_any_to_out_any_tcp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:37,835 | ERROR | Creation failed for RAMSOFTports: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:41,894 | ERROR | Creation failed for CoreFTP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:46,004 | ERROR | Creation failed for PhilipsPacs: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:50,119 | ERROR | Creation failed for Pacs: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:54,206 | ERROR | Creation failed for NexTalk1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:39:58,309 | ERROR | Creation failed for NexTalkTcpUdp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:02,423 | ERROR | Creation failed for CastleSys: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:06,493 | ERROR | Creation failed for FTPpsv5500: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:10,527 | ERROR | Creation failed for Labcorp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:14,649 | ERROR | Creation failed for Labcorptcp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:18,715 | ERROR | Creation failed for IVANStcp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:22,781 | ERROR | Creation failed for IVANSudp: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:26,848 | ERROR | Creation failed for Sophos: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:30,938 | ERROR | Creation failed for any_in_udp_to_any_out: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:35,017 | ERROR | Creation failed for SophosMail: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:39,096 | ERROR | Creation failed for BobSFTP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:43,208 | ERROR | Creation failed for Impulse.UDP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:47,340 | ERROR | Creation failed for ImpulseTCP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:51,410 | ERROR | Creation failed for TEMP_TRACK1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:55,541 | ERROR | Creation failed for PatPortal: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:40:59,610 | ERROR | Creation failed for ALLSCRIPT_PORTAL: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:03,756 | ERROR | Creation failed for testgroup: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:07,801 | ERROR | Creation failed for ALBANYMEDPACS: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:11,855 | ERROR | Creation failed for Guest_Wireless: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:16,023 | ERROR | Creation failed for SOPHOSFTP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:20,116 | ERROR | Creation failed for BOCES_IPADS: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:24,248 | ERROR | Creation failed for TEST: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:28,391 | ERROR | Creation failed for IMO_Ports: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:32,553 | ERROR | Creation failed for TeamViewer: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:36,674 | ERROR | Creation failed for CCD_MESSAGING: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:40,774 | ERROR | Creation failed for Apple_Services: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:44,822 | ERROR | Creation failed for ProviderOrg: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:48,958 | ERROR | Creation failed for MAIL_VIRUS: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:53,025 | ERROR | Creation failed for STAT_RAD: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:41:57,155 | ERROR | Creation failed for StatRadService: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:01,271 | ERROR | Creation failed for PAT_ACCTS_FTP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:05,412 | ERROR | Creation failed for UDP_TEST: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:09,459 | ERROR | Creation failed for CE000SVC: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:13,591 | ERROR | Creation failed for CE2000: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:17,737 | ERROR | Creation failed for mckesson: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:21,863 | ERROR | Creation failed for DM_INLINE_SERVICE_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:25,900 | ERROR | Creation failed for MEDENT_TELEMED: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:29,976 | ERROR | Creation failed for PHINMS: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:34,088 | ERROR | Creation failed for SALUCRO_FTP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:38,211 | ERROR | Creation failed for QUEST_SFTP_NEW: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:42,312 | ERROR | Creation failed for REYHEALTH.EXTERNAL.PORT.GROUP: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:46,372 | ERROR | Creation failed for DM_INLINE_SERVICE_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:46,413 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_service_groups.json
2025-08-04 14:42:50,450 | ERROR | Creation failed for inside_access_in_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:54,384 | ERROR | Creation failed for inside_access_in_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:42:58,320 | ERROR | Creation failed for inside_access_in_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:02,338 | ERROR | Creation failed for inside_access_in_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:06,371 | ERROR | Creation failed for inside_access_in_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:10,310 | ERROR | Creation failed for inside_access_in_rule_6: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:14,290 | ERROR | Creation failed for inside_access_in_rule_7: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:18,270 | ERROR | Creation failed for inside_access_in_rule_8: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:22,246 | ERROR | Creation failed for inside_access_in_rule_9: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:26,225 | ERROR | Creation failed for inside_access_in_rule_10: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:30,192 | ERROR | Creation failed for inside_access_in_rule_11: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:34,155 | ERROR | Creation failed for inside_access_in_rule_12: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:38,186 | ERROR | Creation failed for inside_access_in_rule_13: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:42,125 | ERROR | Creation failed for inside_access_in_rule_14: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:46,057 | ERROR | Creation failed for inside_access_in_rule_15: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:50,042 | ERROR | Creation failed for inside_access_in_rule_16: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:53,997 | ERROR | Creation failed for inside_access_in_rule_17: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:57,919 | ERROR | Creation failed for inside_access_in_rule_18: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:01,855 | ERROR | Creation failed for inside_access_in_rule_19: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:05,816 | ERROR | Creation failed for inside_access_in_rule_20: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:09,760 | ERROR | Creation failed for inside_access_in_rule_21: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:13,751 | ERROR | Creation failed for inside_access_in_rule_22: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:17,750 | ERROR | Creation failed for inside_access_in_rule_23: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:21,719 | ERROR | Creation failed for inside_access_in_rule_24: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:25,702 | ERROR | Creation failed for inside_access_in_rule_25: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:29,675 | ERROR | Creation failed for inside_access_in_rule_26: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:33,655 | ERROR | Creation failed for inside_access_in_rule_27: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:37,602 | ERROR | Creation failed for inside_access_in_rule_28: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:41,592 | ERROR | Creation failed for inside_access_in_rule_29: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:45,576 | ERROR | Creation failed for inside_access_in_rule_30: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:49,508 | ERROR | Creation failed for inside_access_in_rule_31: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:53,499 | ERROR | Creation failed for inside_access_in_rule_32: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:44:57,530 | ERROR | Creation failed for inside_access_in_rule_33: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:01,492 | ERROR | Creation failed for inside_access_in_rule_34: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:05,427 | ERROR | Creation failed for inside_access_in_rule_35: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:09,361 | ERROR | Creation failed for inside_access_in_rule_36: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:13,358 | ERROR | Creation failed for inside_access_in_rule_37: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:17,398 | ERROR | Creation failed for inside_access_in_rule_38: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:21,334 | ERROR | Creation failed for inside_access_in_rule_39: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:25,344 | ERROR | Creation failed for inside_access_in_rule_40: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:29,371 | ERROR | Creation failed for inside_access_in_rule_41: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:33,353 | ERROR | Creation failed for inside_access_in_rule_42: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:37,292 | ERROR | Creation failed for inside_access_in_rule_43: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:41,218 | ERROR | Creation failed for inside_access_in_rule_44: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:45,205 | ERROR | Creation failed for inside_access_in_rule_45: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:49,153 | ERROR | Creation failed for inside_access_in_rule_46: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:53,081 | ERROR | Creation failed for inside_access_in_rule_47: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:45:57,073 | ERROR | Creation failed for inside_access_in_rule_48: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:01,168 | ERROR | Creation failed for inside_access_in_rule_49: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:05,095 | ERROR | Creation failed for inside_access_in_rule_50: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:09,115 | ERROR | Creation failed for inside_access_in_rule_51: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:13,197 | ERROR | Creation failed for inside_access_in_rule_52: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:17,201 | ERROR | Creation failed for inside_access_in_rule_53: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:21,165 | ERROR | Creation failed for inside_access_in_rule_54: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:25,183 | ERROR | Creation failed for inside_access_in_rule_55: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:29,168 | ERROR | Creation failed for inside_access_in_rule_56: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:33,156 | ERROR | Creation failed for inside_access_in_rule_57: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:37,081 | ERROR | Creation failed for inside_access_in_rule_58: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:41,072 | ERROR | Creation failed for inside_access_in_rule_59: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:45,013 | ERROR | Creation failed for inside_access_in_rule_60: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:48,989 | ERROR | Creation failed for inside_access_in_rule_61: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:52,950 | ERROR | Creation failed for inside_access_in_rule_62: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:56,878 | ERROR | Creation failed for inside_access_in_rule_63: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:00,803 | ERROR | Creation failed for inside_access_in_rule_64: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:04,771 | ERROR | Creation failed for inside_access_in_rule_65: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:08,711 | ERROR | Creation failed for inside_access_in_rule_66: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:12,723 | ERROR | Creation failed for inside_access_in_rule_67: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:16,687 | ERROR | Creation failed for inside_access_in_rule_68: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:20,736 | ERROR | Creation failed for inside_access_in_rule_69: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:24,691 | ERROR | Creation failed for inside_access_in_rule_70: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:28,614 | ERROR | Creation failed for inside_access_in_rule_71: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:32,587 | ERROR | Creation failed for inside_access_in_rule_72: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:36,553 | ERROR | Creation failed for inside_access_in_rule_73: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:40,501 | ERROR | Creation failed for inside_access_in_rule_74: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:44,435 | ERROR | Creation failed for inside_access_in_rule_75: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:48,399 | ERROR | Creation failed for inside_access_in_rule_76: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:52,367 | ERROR | Creation failed for inside_access_in_rule_77: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:47:56,327 | ERROR | Creation failed for inside_access_in_rule_78: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:00,259 | ERROR | Creation failed for inside_access_in_rule_79: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:04,227 | ERROR | Creation failed for inside_access_in_rule_80: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:08,218 | ERROR | Creation failed for inside_access_in_rule_81: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:12,162 | ERROR | Creation failed for inside_access_in_rule_82: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:16,101 | ERROR | Creation failed for inside_access_in_rule_83: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:20,060 | ERROR | Creation failed for inside_access_in_rule_84: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:24,047 | ERROR | Creation failed for inside_access_in_rule_85: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:28,010 | ERROR | Creation failed for inside_access_in_rule_86: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:31,975 | ERROR | Creation failed for inside_access_in_rule_87: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:35,940 | ERROR | Creation failed for inside_access_in_rule_88: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:39,941 | ERROR | Creation failed for inside_access_in_rule_89: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:43,929 | ERROR | Creation failed for inside_access_in_rule_90: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:47,875 | ERROR | Creation failed for outside_access_in_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:51,789 | ERROR | Creation failed for outside_access_in_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:55,782 | ERROR | Creation failed for outside_access_in_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:48:59,773 | ERROR | Creation failed for outside_access_in_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:03,770 | ERROR | Creation failed for outside_access_in_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:07,749 | ERROR | Creation failed for outside_access_in_rule_6: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:11,766 | ERROR | Creation failed for outside_access_in_rule_7: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:15,754 | ERROR | Creation failed for outside_access_in_rule_8: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:19,767 | ERROR | Creation failed for outside_access_in_rule_9: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:23,754 | ERROR | Creation failed for outside_access_in_rule_10: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:27,804 | ERROR | Creation failed for outside_access_in_rule_11: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:31,783 | ERROR | Creation failed for outside_access_in_rule_12: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:35,721 | ERROR | Creation failed for outside_access_in_rule_13: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:39,665 | ERROR | Creation failed for outside_access_in_rule_14: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:43,715 | ERROR | Creation failed for outside_access_in_rule_15: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:47,670 | ERROR | Creation failed for outside_access_in_rule_16: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:51,609 | ERROR | Creation failed for outside_access_in_rule_17: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:55,601 | ERROR | Creation failed for outside_access_in_rule_18: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:49:59,591 | ERROR | Creation failed for outside_access_in_rule_19: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:03,514 | ERROR | Creation failed for outside_access_in_rule_20: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:07,514 | ERROR | Creation failed for outside_access_in_rule_21: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:11,497 | ERROR | Creation failed for outside_access_in_rule_22: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:15,468 | ERROR | Creation failed for outside_access_in_rule_23: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:19,477 | ERROR | Creation failed for outside_access_in_rule_24: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:23,443 | ERROR | Creation failed for outside_access_in_rule_25: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:27,408 | ERROR | Creation failed for outside_access_in_rule_26: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:31,392 | ERROR | Creation failed for outside_access_in_rule_27: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:35,386 | ERROR | Creation failed for outside_access_in_rule_28: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:39,336 | ERROR | Creation failed for outside_access_in_rule_29: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:43,304 | ERROR | Creation failed for outside_access_in_rule_30: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:47,271 | ERROR | Creation failed for outside_access_in_rule_31: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:51,201 | ERROR | Creation failed for outside_access_in_rule_32: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:55,135 | ERROR | Creation failed for outside_access_in_rule_33: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:50:59,153 | ERROR | Creation failed for outside_access_in_rule_34: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:03,209 | ERROR | Creation failed for outside_access_in_rule_35: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:07,150 | ERROR | Creation failed for outside_access_in_rule_36: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:11,131 | ERROR | Creation failed for outside_access_in_rule_37: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:15,153 | ERROR | Creation failed for outside_access_in_rule_38: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:19,183 | ERROR | Creation failed for outside_access_in_rule_39: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:23,169 | ERROR | Creation failed for outside_access_in_rule_40: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:27,145 | ERROR | Creation failed for outside_access_in_rule_41: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:31,090 | ERROR | Creation failed for outside_access_in_rule_42: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:35,065 | ERROR | Creation failed for outside_access_in_rule_43: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:39,018 | ERROR | Creation failed for outside_access_in_rule_44: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:43,093 | ERROR | Creation failed for outside_access_in_rule_45: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:47,040 | ERROR | Creation failed for outside_access_in_rule_46: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:51,091 | ERROR | Creation failed for outside_access_in_rule_47: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:55,018 | ERROR | Creation failed for outside_access_in_rule_48: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:51:58,944 | ERROR | Creation failed for outside_access_in_rule_49: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:02,923 | ERROR | Creation failed for outside_access_in_rule_50: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:06,954 | ERROR | Creation failed for outside_access_in_rule_51: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:10,877 | ERROR | Creation failed for DMZ_access_in_V1_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:14,848 | ERROR | Creation failed for DMZ_access_in_V1_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:18,873 | ERROR | Creation failed for DMZ_access_in_V1_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:22,883 | ERROR | Creation failed for DMZ_access_in_V1_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:26,807 | ERROR | Creation failed for DMZ_access_in_V1_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:30,754 | ERROR | Creation failed for DMZ_access_in_V1_rule_6: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:34,732 | ERROR | Creation failed for inside_nat0_outbound_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:38,791 | ERROR | Creation failed for inside_nat0_outbound_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:42,817 | ERROR | Creation failed for inside_nat0_outbound_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:46,750 | ERROR | Creation failed for inside_nat0_outbound_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:50,686 | ERROR | Creation failed for inside_nat0_outbound_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:54,684 | ERROR | Creation failed for inside_nat0_outbound_rule_6: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:52:58,684 | ERROR | Creation failed for inside_nat0_outbound_rule_7: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:02,620 | ERROR | Creation failed for inside_nat0_outbound_rule_8: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:06,596 | ERROR | Creation failed for inside_nat0_outbound_rule_9: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:10,624 | ERROR | Creation failed for inside_nat0_outbound_rule_10: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:14,601 | ERROR | Creation failed for inside_nat0_outbound_rule_11: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:18,592 | ERROR | Creation failed for inside_nat0_outbound_rule_12: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:22,634 | ERROR | Creation failed for inside_nat0_outbound_rule_13: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:26,770 | ERROR | Creation failed for inside_nat0_outbound_rule_14: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:30,748 | ERROR | Creation failed for inside_nat0_outbound_rule_15: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:34,760 | ERROR | Creation failed for inside_nat0_outbound_rule_16: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:38,702 | ERROR | Creation failed for inside_nat0_outbound_rule_17: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:42,676 | ERROR | Creation failed for inside_nat0_outbound_rule_18: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:46,647 | ERROR | Creation failed for inside_nat0_outbound_rule_19: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:50,579 | ERROR | Creation failed for VPN2.nlh.org_splitTunnelAcl_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:54,529 | ERROR | Creation failed for outside_cryptomap_6_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:53:58,543 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:02,481 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:06,420 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:10,360 | ERROR | Creation failed for outside_cryptomap_3_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:14,361 | ERROR | Creation failed for outside_cryptomap_9_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:18,370 | ERROR | Creation failed for outside_cryptomap_10_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:22,410 | ERROR | Creation failed for outside_cryptomap_11_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:26,391 | ERROR | Creation failed for outside_cryptomap_1_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:30,389 | ERROR | Creation failed for outside_cryptomap_12_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:34,328 | ERROR | Creation failed for outside_cryptomap_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:38,284 | ERROR | Creation failed for outside_cryptomap_14_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:42,230 | ERROR | Creation failed for outside_cryptomap_15_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:46,223 | ERROR | Creation failed for outside_cryptomap_2_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:50,163 | ERROR | Creation failed for outside_cryptomap_7_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:54,121 | ERROR | Creation failed for outside_cryptomap_880_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:54:58,079 | ERROR | Creation failed for outside_pnat_inbound_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:02,046 | ERROR | Creation failed for inside_pnat_outbound_V2_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:06,006 | ERROR | Creation failed for outside_cryptomap_1000_1_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:09,968 | ERROR | Creation failed for outside_cryptomap_13_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:13,906 | ERROR | Creation failed for outside_cryptomap_16_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:17,910 | ERROR | Creation failed for outside_cryptomap_1120_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:21,874 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:25,810 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:29,804 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:33,790 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:37,726 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:41,683 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:45,794 | ERROR | Creation failed for outside_cryptomap_17_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:49,876 | ERROR | Creation failed for inside_pnat_outbound_V14_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:53,845 | ERROR | Creation failed for inside_pnat_outbound_V15_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:55:57,806 | ERROR | Creation failed for outside_cryptomap_5_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:01,765 | ERROR | Creation failed for outside_cryptomap_1300_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:05,754 | ERROR | Creation failed for sfr_redirect_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:09,701 | ERROR | Creation failed for Guest_access_in_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:13,810 | ERROR | Creation failed for Guest_access_in_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:17,765 | ERROR | Creation failed for Guest_access_in_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:21,799 | ERROR | Creation failed for Vendor_access_in_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:25,782 | ERROR | Creation failed for Vendor_access_in_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:29,731 | ERROR | Creation failed for Vendor_access_in_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:33,752 | ERROR | Creation failed for Vendor_access_in_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:37,777 | ERROR | Creation failed for Vendor_access_in_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:41,930 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:45,886 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_2: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:49,957 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_3: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:54,000 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_4: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:56:57,934 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_5: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:01,976 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_6: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:05,939 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_7: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:09,933 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_8: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:13,884 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_9: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:17,846 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_10: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:21,806 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_11: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:25,798 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_12: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:29,787 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_13: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:33,751 | ERROR | Creation failed for outside_cryptomap_4_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:37,702 | ERROR | Creation failed for outside_cryptomap_8_rule_1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:57:37,755 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754328956_phase1_access_rules.json
2025-08-04 14:57:37,757 | INFO | ================================================================================
2025-08-04 14:57:37,758 | INFO | MIGRATION SUMMARY
2025-08-04 14:57:37,759 | INFO | ================================================================================
2025-08-04 14:57:37,761 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:57:37,762 | INFO |    • Total Objects: 1122
2025-08-04 14:57:37,764 | INFO |    • Created: 0
2025-08-04 14:57:37,765 | INFO |    • Updated: 666
2025-08-04 14:57:37,766 | INFO |    • Failed: 456
2025-08-04 14:57:37,768 | INFO |    • Skipped: 0
2025-08-04 14:57:37,769 | INFO |    • Success Rate: 59.4%
2025-08-04 14:57:37,770 | INFO | ================================================================================
2025-08-04 14:57:37,786 | INFO | [FILE] Summary saved: migration_summary_migration_1754328956.json
2025-08-04 14:57:37,789 | WARNING | [WARN]  Migration completed with 456 failures
