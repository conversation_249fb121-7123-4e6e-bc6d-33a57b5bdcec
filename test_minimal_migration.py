#!/usr/bin/env python3
"""
Test Minimal Migration
Tests the FMC migration system with the minimal configuration to identify and fix issues.
"""

import json
import sys
import os
from datetime import datetime
from fmc_migration_v2 import FMCMigrationEngine

def test_migration_with_minimal_config():
    """Test migration with minimal configuration"""
    
    print("=" * 80)
    print("🧪 FMC Migration System - Minimal Migration Test")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configuration for testing
    config_file = "minimal_validation_config.json"
    
    # FMC connection details (update these for your environment)
    fmc_host = "https://*************"  # Update this to your FMC IP
    username = "admin"                   # Update this
    password = "!Techn0l0gy01!"         # Update this
    
    print("🔧 Configuration:")
    print(f"   • Config File: {config_file}")
    print(f"   • FMC Host: {fmc_host}")
    print(f"   • Username: {username}")
    print(f"   • SSL Verification: Disabled")
    print()
    
    # Load configuration
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        print(f"✅ Loaded configuration with {len(config.get('api_calls', {}))} object types")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    # Initialize migration engine
    try:
        print("🔄 Initializing migration engine...")
        engine = FMCMigrationEngine(
            fmc_host=fmc_host,
            username=username,
            password=password,
            verify_ssl=False,
            overwrite=False,
            quiet=False
        )
        print("✅ Migration engine initialized")
    except Exception as e:
        print(f"❌ Failed to initialize migration engine: {e}")
        print("💡 Check FMC connectivity and credentials")
        return False
    
    print()
    print("🚀 Starting migration test...")
    print("-" * 40)
    
    try:
        # Run migration
        results = engine.migrate_configuration(config)
        
        print()
        print("📊 Migration Results:")
        print("-" * 40)
        
        total_objects = 0
        total_created = 0
        total_updated = 0
        total_failed = 0
        
        for phase_name, result in results.items():
            if hasattr(result, 'total_objects'):
                print(f"📋 {phase_name}:")
                print(f"   • Total: {result.total_objects}")
                print(f"   • Created: {result.created}")
                print(f"   • Updated: {result.updated}")
                print(f"   • Failed: {result.failed}")
                print(f"   • Success Rate: {result.success_rate:.1f}%")
                
                total_objects += result.total_objects
                total_created += result.created
                total_updated += result.updated
                total_failed += result.failed
                
                # Show first few details
                if result.details:
                    print(f"   • Details (first 3):")
                    for detail in result.details[:3]:
                        print(f"     - {detail}")
                print()
        
        print("=" * 40)
        print(f"📈 Overall Summary:")
        print(f"   • Total Objects: {total_objects}")
        print(f"   • Created: {total_created}")
        print(f"   • Updated: {total_updated}")
        print(f"   • Failed: {total_failed}")
        
        if total_objects > 0:
            success_rate = ((total_created + total_updated) / total_objects) * 100
            print(f"   • Success Rate: {success_rate:.1f}%")
        
        # Determine test result
        if total_failed == 0:
            print("🎉 Migration test PASSED - All objects processed successfully!")
            return True
        elif total_failed < total_objects / 2:
            print("⚠️  Migration test PARTIAL - Some objects failed")
            print("💡 Check logs for specific error details")
            return True
        else:
            print("❌ Migration test FAILED - Most objects failed")
            print("💡 Check FMC connectivity and object definitions")
            return False
            
    except Exception as e:
        print(f"❌ Migration failed with exception: {e}")
        print("💡 Check logs for detailed error information")
        return False

def main():
    """Main function"""
    print("Starting minimal migration test...")
    print("This test will attempt to create a small set of objects in FMC")
    print("to validate the migration system functionality.")
    print()
    
    # Check if user wants to proceed
    response = input("Do you want to proceed with the test? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Test cancelled by user.")
        return
    
    success = test_migration_with_minimal_config()
    
    if success:
        print()
        print("✅ Test completed successfully!")
        print("💡 You can now proceed with larger configurations")
    else:
        print()
        print("❌ Test failed - please review the issues above")
        print("💡 Fix the identified issues before proceeding")

if __name__ == "__main__":
    main()
