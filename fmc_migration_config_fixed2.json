{"metadata": {"source": "ASA Configuration Migration", "timestamp": "2025-01-08", "total_objects": {"host_objects": 629, "network_objects": 63, "service_objects": 29, "object_groups": 177, "access_rules": 224}, "last_fixed": "2025-08-04T12:15:03.880511", "fixed_by": "fix_config_issues.py"}, "api_calls": {"host_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/hosts", "method": "POST", "data": [{"name": "RadSaratoga", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RadAmsMem", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RadStMarys", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RadSeton", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RadBellevue", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CITRIXFS02", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP30", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGIC_C-iDRAC", "type": "Host", "value": "**************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHNAS12", "type": "Host", "value": "**************8", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGIC-D_iDRAC", "type": "Host", "value": "**************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP02", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHNAS11", "type": "Host", "value": "**************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGIC_A-iDRAC", "type": "Host", "value": "**************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGIC_E-iDRAC", "type": "Host", "value": "**************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGIC_D-NEWiSCSI", "type": "Host", "value": "**************3", "description": "Migrated from ASA - ", "overridable": false}, {"name": "UNITY-SDC_iSCSI", "type": "Host", "value": "**************4", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-WEB01-WS01", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICA", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICC", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICD", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICE", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICB", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICF", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DICTATION02", "type": "Host", "value": "*************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDILIVE.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_FS_SEC", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CTScanner", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "<PERSON><PERSON><PERSON>", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RandF", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PetLinks1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PetLinks2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAGICD3_DRAC", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LIEBERT.2FL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal132", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal133", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal144", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal145", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal176", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal177", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal194", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Cardinal195", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "<PERSON><PERSON><PERSON>", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "medinote2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "medinote1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN3.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN5.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN1.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN4.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN9.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN10.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN11.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACS2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACS1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACS3", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACS4", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACS5", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHEXCHANGE.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB_VIP_NETSCALER1", "type": "Host", "value": "**************1", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_CACHE", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_STORE1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_STORE2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_STORE144", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ResnickPacs1", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ResnickPACS2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleRadPC", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CatScan", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PERTH_MRI", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PETScanCT", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XELERIS", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "INFINIA", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "D5000", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Ultrasound1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Ultrasound2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Ultrasound3", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "KonicaJM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Konicardr1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "KonicaRdr2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "KonicaRdr3", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_NEW", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "US_LOGI_E9", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk242", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk243", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk244", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk245", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk246", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalk247", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalkPrime", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NexTalkSec", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Spantel.Prod", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SpantelHL7.test", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "eRXcenter2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "eRXcenter3", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "eRXcenter1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "eRxChicago", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "eRxDallas", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MedentRemote", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Medent.RPTS", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STpc.rtr", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "spc.rtr", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ppc.pix", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.ps1", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.ps2", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.ps3", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.ps4", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.syn1", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.syn2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.orpc1", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.orpc2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.orpc3", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read3", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.KPServer", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read4", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "smha.mammo", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "smha.pacsed30", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "smha.pacrd06", "type": "Host", "value": "*********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read5", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read6", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SHMA.read7", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read8", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read9", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.read10", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.Synapse.Dest", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P-DI-MGR", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_READ3_NEW", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_CIO1", "type": "Host", "value": "**************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_DI_NUMED", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MAMMO40", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MOMMO41", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "phil<PERSON><PERSON><PERSON>", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSP19WEB.NLH.ORG", "type": "Host", "value": "*************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_REP1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_REP5", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PCC_BILL1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_REP6", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_REP3", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_REP4", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SOPHOSEMAIL", "type": "Host", "value": "**************1", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SOPHOSWEB", "type": "Host", "value": "**************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NATHAN6.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ORTIZ_LT", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "p_mis_netadmin", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_OR3", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_OR1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_OR2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_DI", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDC1_IPMI", "type": "Host", "value": "**************8", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDC2_IPMI", "type": "Host", "value": "**************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AAI.120", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AAI.124", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AAI.125", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AAI.52", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "INTERLACE", "type": "Host", "value": "*************8", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHUTILITY", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHTEST01-NIC2", "type": "Host", "value": "*************51", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BPC-UPS", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ALBANYMED.IN.2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ALBANYMED.IN", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "hixny.com_integration", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "hixny.com_prod", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "webservices.hixny.com", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDITEST", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT03", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB_VIRTUALIP", "type": "Host", "value": "**************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB_VIRTUALIP_NETSCALER2", "type": "Host", "value": "**************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT05", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PHA_WS3", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PHA_WS2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_MR_SCAN1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "easyeeg", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VENUE50_p_pacs_cdburn", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "integration.hixny.com", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Hixney.net_2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CITRIX_STOREFRONT", "type": "Host", "value": "*************53", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P-IT-MGR", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETSCALER.VPX", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETSCALER.WEB", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETSCALERSUBNETIP", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDC01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDC02.NLH.ORG", "type": "Host", "value": "*************13", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_CISCO_01", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "p_mis_netadmin2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Direct.Hixny.Com", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Healthstream.SMPT.Peer", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Hixny.net", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Hixny.com", "type": "Host", "value": "************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "statrad.hl7.test", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PAT_FIN", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHENDO01", "type": "Host", "value": "*************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHENDO01.NLH.ORG", "type": "Host", "value": "*************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ENDOWORKS02", "type": "Host", "value": "*************1", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ENDOWORKS03", "type": "Host", "value": "*************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IS_PACS", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "retsolinc2.com", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "retsolinc3.com", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SophosMailExt", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "IRIS", "type": "Host", "value": "*********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "smtp.biz.rr.com", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Hypertype", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MVP", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LeaderHFTPsite", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LeaderHFTPsite2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "stentor.com", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TOGARM", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Infotrak", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "sftp.lifethc.org", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleVideo1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Televid2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CONNECTPLUS01", "type": "Host", "value": "*************3", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VeriquestPC", "type": "Host", "value": "**************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VeriquestSite", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT_PORTAL_1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HYPER-_REPLICA_BROKER", "type": "Host", "value": "*************6", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Sodexho", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Provation-out", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VeriquestServer", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "<PERSON><PERSON>", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "IMO_2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "WWW.UPTODATE.COM", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP22", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HYPER-V_CLUSTER", "type": "Host", "value": "*************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENTPORTAL.EXTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "remote.nlh.org", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "mail.nlh.org", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DIRECT.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleMed_1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDI.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHCISCO", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LabCorp3", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LabCorpDev", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LabCorpProd", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TheOutsourceGroup", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleradIT_Millenium1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleradIT_Millenium2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FastChart.Inside", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Ellis.inside", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STATRAD.DR.SVR", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHTEST01", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH.ORG.EXTERNAL.FORMS", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-************", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-***********", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-***********", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-************", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-***********", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-************", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-************", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Ellis.Peer.New", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH.PEER.INTERNAL.1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Medent.Peer.New.", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.MBMS.MILLENIUMBILLING.PEER", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MCKESSON.MC.PHARM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "newsync3.mkesson.com", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-0.0.0.0", "type": "Host", "value": "0.0.0.0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.pacs1", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.pacs2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.pacs3", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS.VCE1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS.VCE2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS.VCE3", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS.VCE4", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT_NAS_INTERNAL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH02", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PDX.Internal", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PDX.External", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.PEER.NEW", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.INTERNAL1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XCHANGEWORX.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETSCALER.NLHRESTAPI", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA_VPN_NLH_PEER1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA_VPN_NLH_PEER2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FIREPOWER_VM_ESXI", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.READ.10", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ESRS_EMC_VIRTUAL_APPLIANCE", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB.INTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB.DMZ", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RESTFULAPI.DMZ", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.1", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.EXTERNAL.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA.INTERNAL.1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA.INTERNAL.2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_MIS52_DMZ", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDITEST_SENDTRYDS", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP25", "type": "Host", "value": "*************3", "description": "Migrated from ASA - ", "overridable": false}, {"name": "skype.nlh.org_external", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "st_netadmin", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.RAD.EXTERNAL", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MVO_AMST_PEER_NEW", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GUEST_INTERFACE_EXTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VENDOR_EXTERNAL_INTERFACE", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "p_mis_netadmin.dmz", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-ISWEB.DMZVR", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AMC.PACS.NEW", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BRIAN_DHCP", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BPC.External", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_MIS_CISCOMON", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP17", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP18", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP19", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_MIS52.WAYNE", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETADMIN.DMZ.TEST", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "EUGENE10", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDITECHAPIVIP1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDITECHAPIVIP2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "StratSolution.Peer", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_PHA_PDX1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHPRTG01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RCARE-SERVER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDMZ01_SWITCH", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PRTG.NLH.ORG.EXTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BACKLINE.VPN.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "UNITEDLABNETWORK.VPN.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_**************", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BACKLINE.LDAP.INTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT.NIMBLE.INSIDE.1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ROBOT_GE_VOT_TRAIN", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BILL_BAIRD", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS29-iDRAC", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DOLBEY", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DOLBEYTEST", "type": "Host", "value": "*************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH_DCDS_9300s", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Schumacher.Inside1.new.ADTPROD", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Schumacher.Inside2.new.ADTTEST", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Schumacher.VPN.Peer.New", "type": "Host", "value": "*************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENT-EXPORT", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUEST.VPN.PEER.2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUEST.VPN.INTERNAL.2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "<PERSON>", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.PEER.INTERNAL.TEST", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.PEER.INTERNAL.PROD", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSP19OFCWEB.NLH.ORG", "type": "Host", "value": "**************8", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENTPORTAL.DMZ", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "mtrestexpapis-live01.nlh.org.external", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "mtrestexpapis-test01.nlh.org.external", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "mtrestexpapis-test01.nlh.org.DMZ", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "mtrestexpapis-live01.nlh.org.DMZ", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHANGE.HEALTHCARE.EXTERNAL.PEER", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI.T.BG01", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHC.EXTERNAL.1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHC.EXTERNAL.2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-T-BG01.CHC.NAT", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDILIVE.CHC.NAT", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDITEST.CHC.NAT", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-T-BG01", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHFTP01.NLH.ORG", "type": "Host", "value": "**************4", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SR_STACK_01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-BG01.nlh.org", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-BG04.CHC.NAT", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-BG04", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHC.EXTERNAL.3", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.3", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "WEBSSO.MEDITECH.COM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "WEBSSO2FA.MEDITECH.COM", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.INTERNAL.PUSH_SERVER", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIXNY.INTERNAL.TESTHUB", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FIRECALL_JSC", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FIRECALLSYSTEM_ENDPOINTS1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FIRECALLSYSTEM_ENDPOINTS2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BACKLINE.VPN.PEER2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BACKLINE.LDAP.INTERNAL2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_*************", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BANDWIDTH_TEST", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IS_1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IS_3", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IT_COOR", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IT_TECH1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HIRAM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RYAN", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NICK", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "IT_TEST", "type": "Host", "value": "**************6", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P-BOARDROOM1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS08", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS21-iLO", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHADMINCENTER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP24", "type": "Host", "value": "*************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SQL01.NLH.ORG", "type": "Host", "value": "*************3", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FAXSERVER.NLH.ORG", "type": "Host", "value": "*************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHFUSION", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BACKUPEXEC01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ARCHIVE.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PRINT", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHBACKUP", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "INTERLACETEST", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHMONITOR01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SANPHNHM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK_BCR", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK_VISTA2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK_VISTA1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK_LCM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS31-iDRAC", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP21", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHCITRIXGATEWAY", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ST_NETADMIN2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DR_CECIL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P_IS_RAMANI", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "US_LOGU_E9_2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.4", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLI-BG13", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHTESTMOBILE", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NOVA.NLH.ORG.EXTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BANDWIDTH_TEST_2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_***************", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_************", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Barracuda.Web.NLH.Internal", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Barracuda.Email.NLH.Internal", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH.EXTERNAL.PEER", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH.PEER.INTERNAL.2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_*************", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DMZ_TEST.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DUOTEST.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DUOTEST.NLH.ORG.DMZ", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BARRACUDA.EMAIL.INSIDE", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH.CORE.INTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DCDS.CORE.INTERNAL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GPC_STACK", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSSI", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHBRAUNPUMPS.INTERNAL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHBRAUNPUMPS.EXTERNAL", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "P-ITMGR", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDIVATOR66838147", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDIVATOR66838143", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AMC.VPN.PEER.NEW", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA.INTERNAL.NEW.1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA.INTERNAL.NEW.2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ULN.VPN.INTERNAL", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CISCOPRIME.INTERNAL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CISCOPRIMEINF", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MIS_TEST2", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CISCONMON", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SYSLOGSERVER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NOVA-QIE.INTERNAL", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NOVA.INTERLACE.PEER.EXTERNAL", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "WLC1", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "sendgrid.net.virus", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NOVA.INTERLACE.PEER.EXTERNAL2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "love.explorethebest.com.spam.2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "love.explorethebest.com.spam.1", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "love.explorethebest.com.spam.3", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CISCO.WSA.INTERNAL", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HARRIET.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS32.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS10A.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS19B.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "WILLYWONKA.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSYN01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSYN02.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSYN03.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSYN04.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSP19APP.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS18C.NLH.ORG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS19C.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS14.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS26D.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DDPC.FIREALARM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCUIS16B.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS17B.NLH.ORG", "type": "Host", "value": "**************9", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS19A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SUMMIT.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS25A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ONEVIEW.NLH.ORG", "type": "Host", "value": "**************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DR1.NLH.ORG", "type": "Host", "value": "**************3", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS26B.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHBACKUP02.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "KRONOSNEW.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHA.RAD.EXTERNAL.NEW", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BARRACUDA.LDAP.EXTERNAL.PEER.1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BARRACUDA.LDAP.EXTERNAL.PEER.2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BARRACUDA.LDAP.EXTERNAL.PEER.3", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "REYHEALTH.EXTERNAL.EXTERNAL.1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "REYHEALTH.EXTERNAL.EXTERNAL.2", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHC.OPTUM.EXTERNAL.VPN.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS18D.NLH.ORG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STREAMTASK.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GPSUPPORT.VPN.EXTERNAL.PEER", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.AWSERVER", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.AWSERVER.ILO", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.CTSCANNER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.CT.ADV.WS", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.GE.MAMMO.INTERFACE", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MAMMO.SHUTTLE", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MRI.ALLIANCE", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MUSE01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MUSE02", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MUSE03", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.MAMMO", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.NUCMEDCAMERA", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.PETCTVIEWER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.PERTH.XRAY", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.R.AND.F", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.ROOMA", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.XELERIS.NM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.5", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CLEARWATER1", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CLEARWATER2", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "JELMENDORFSPAM", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS25C.NLH.ORG", "type": "Host", "value": "*************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PROVMDAPP.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHPROVMDORACLE.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHMUSE01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHMUSE02.NLH.ORG", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS16A.NLH.ORG", "type": "Host", "value": "**************5", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DESIGO.NLH.ORG", "type": "Host", "value": "**************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIOUS01", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHPRTGPROBE04", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS10C", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS28", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS07.NLH.ORG", "type": "Host", "value": "**************4", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NURSECALLAPP.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHBRAUNPUMPS.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BRAUNWEB", "type": "Host", "value": "*************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS09A.NLH.ORG", "type": "Host", "value": "*************04", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS09B.NLH.ORG", "type": "Host", "value": "*************05", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS09C.NLH.ORG", "type": "Host", "value": "*************06", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHCISCO.NLH.ORG", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS13.NLH.ORG", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SQLTEST.NLH.ORG", "type": "Host", "value": "**************4", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHMONITOR.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHPRTG01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHKIWISYSLOG01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS17A.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CITRIXSF.NLH.ORG", "type": "Host", "value": "*************53", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHWEB01..NLH.ORG", "type": "Host", "value": "*************03", "description": "Migrated from ASA - ", "overridable": false}, {"name": "AVAYACALLACCT.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHSSI.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TEMPTRAK.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PRINT.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUICKCHARGE.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH3M.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS19D.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHAV01.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS23A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS23B.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS23C.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS23D.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDHCP01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS25B.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS21.NLH.ORG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CENTRALINK.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS27.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH02.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MUSE03.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "KRONOSTEST.NLH.ORG", "type": "Host", "value": "*************6", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MUSE-TEST.NLH.ORG", "type": "Host", "value": "**************6", "description": "Migrated from ASA - ", "overridable": false}, {"name": "INTERLACETEST.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHINT-TEST.NLH.ORG", "type": "Host", "value": "**************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS29.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHFUSION.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MUSE-CCGHL7.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS31.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS10B.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS10C.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHCA.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHPRTGPROBE3.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS10D.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CODONICS.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MDITEST.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CITRIXFS02.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "XENAPP02.NLH.ORG", "type": "Host", "value": "**************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MEDENTPRINT01.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HEALTHTOUCH01.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS18A.NLH.ORG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "INTERLACE.NLH.ORG", "type": "Host", "value": "*************8", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NOVA-QIE.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS18B.NLH.ORG", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHUTILITY.NLH.ORG", "type": "Host", "value": "*************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHCODONICS.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHLICENSE.NLH.ORG", "type": "Host", "value": "*************1", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HPDMAN.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SCVMM.NLH.ORG", "type": "Host", "value": "**************2", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS24A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS24B.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS24C.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS24D.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS26A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ESICALLACCT26A.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ESRS.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHDRFIRST.NLH.ORG", "type": "Host", "value": "**************7", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHELOCK.NLH.ORG", "type": "Host", "value": "*************1", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS26C.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "COBAS.NLH.ORG", "type": "Host", "value": "*************6", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PRADEV.NLH.ORG", "type": "Host", "value": "***************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLHADMINCENTER.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS28.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NURSECALLHD.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH-iUV.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LUCIUS30.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MUSE-APP.NLH.ORG", "type": "Host", "value": "**************0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MUSE-NXWEB.NLH.ORG", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Clearwater.External.Peer", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ASA01", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ASA02", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_*************", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_*************", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1", "type": "Host", "value": "**********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MMI.BILLING.EXTERNAL.PEER", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MMI.BILLING.INTERNAL.PEER", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.MEDICOM", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.AMBRA", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.POWERSHARE", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NYOH.INTERNAL.CLOUD", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Nuvodia.OneOncology.Cloud.External.Peer", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Nuvodia.OneOncology.Cloud.Internal.Peer", "type": "Host", "value": "**************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NETWORK_OBJ_*************", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA.INTERNAL.NEW.3", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.1", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.2", "type": "Host", "value": "***********", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.3", "type": "Host", "value": "************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.5", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.6", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}, {"name": "FRESHWORKS.EXCLUSIONS.7", "type": "Host", "value": "*************", "description": "Migrated from ASA - ", "overridable": false}]}, "network_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networks", "method": "POST", "data": [{"name": "TeleMedVT3", "type": "Network", "value": "************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TelemedVT4", "type": "Network", "value": "************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TelemedVT5", "type": "Network", "value": "*************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "TeleMedVT1", "type": "Network", "value": "************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Medent.VPN.net", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SMHApacsSUBNET", "type": "Network", "value": "*************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "pacs.net", "type": "Network", "value": "***************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PACS_VCE", "type": "Network", "value": "*************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "pacs.net_1", "type": "Network", "value": "***************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Olympus.Inside.New", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "speculator", "type": "Network", "value": "***************/27", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GEserviceNET", "type": "Network", "value": "*********/16", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Mill.PACS.NET", "type": "Network", "value": "**********/16", "description": "Migrated from ASA - ", "overridable": false}, {"name": "DI.NET", "type": "Network", "value": "*************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STUDENT_VLAN", "type": "Network", "value": "*********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "questlab", "type": "Network", "value": "*************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "iPEOPLEremote", "type": "Network", "value": "************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LAN", "type": "Network", "value": "*************/20", "description": "Migrated from ASA - ", "overridable": false}, {"name": "RALSplusLAN", "type": "Network", "value": "*************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "PhilipsSupport", "type": "Network", "value": "***********/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STRAT_SOL.NET.INTERNAL1", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MVOrtho.net", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LAN_1", "type": "Network", "value": "*************/23", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACSnat", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MVOatJSC.net", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SENTRYDS.NET", "type": "Network", "value": "***********/29", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SENTRYDS", "type": "Network", "value": "***************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "pacs.net-01", "type": "Network", "value": "***************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "LAN-01", "type": "Network", "value": "*************/23", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MilleniumPACSnat-*************", "type": "Range", "value": "***********/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj-*************-*************", "type": "Range", "value": "*************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj_any", "type": "Network", "value": "0.0.0.0/0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "obj_any-03", "type": "Network", "value": "0.0.0.0/0", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NUVODIA_NETWORK_3", "type": "Network", "value": "*************/29", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GUEST_WLAN_NAT", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GUEST_NETWORK", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "VENDOR_WLAN_NAT", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CREDITCARD_CAFE_EXTERNAL1", "type": "Network", "value": "*************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CREDITCARD_CAFE2_EXTERNAL2", "type": "Network", "value": "*************/27", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CREDITCARD_CAFE_EXTERNAL", "type": "Network", "value": "*************/27", "description": "Migrated from ASA - ", "overridable": false}, {"name": "STRAT_SOL.NET.INTERNAL2", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "EXPANSE_VLAN1", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "EXPANSE_VLAN2", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "EXPANSE_VLAN3", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "EXPANSE_VLAN4", "type": "Network", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "QUEST.VPN.EXTERNAL.2", "type": "Network", "value": "************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CHC.OPTUM.NAT.INTERNAL.SUB", "type": "Range", "value": "***************/27", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ACRONIS.EXTERNAL.RANGE1", "type": "Range", "value": "*************/25", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ACRONIS.EXTERNAL.RANGE2", "type": "Range", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "BARRACUDA.CLOUD.EXTERNAL", "type": "Range", "value": "************/21", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ACRONIS.EXTERNAL.RANGE3", "type": "Network", "value": "***********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "ACRONIS.EXTERNAL.RANGE4", "type": "Network", "value": "*************/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "GESUPPORT.INTERNAL.NET", "type": "Network", "value": "*********/16", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CLEARWATER3", "type": "Range", "value": "**********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "CLEARWATER4", "type": "Range", "value": "************/23", "description": "Migrated from ASA - ", "overridable": false}, {"name": "backblazeb2.com", "type": "FQDN", "value": "backblazeb2.com", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HANYS.EXTERNAL.1", "type": "FQDN", "value": "46453879m.hanys.org", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HANYS.INTERNAL.2", "type": "FQDN", "value": "mta0102-101.cd.hanys.org", "description": "Migrated from ASA - ", "overridable": false}, {"name": "HANYS.EXTERNAL.3", "type": "FQDN", "value": "mta0203-229.cd.hanys.org", "description": "Migrated from ASA - ", "overridable": false}, {"name": "Clearwater.Internal.Peer.Range", "type": "Network", "value": "*************/28", "description": "Migrated from ASA - ", "overridable": false}, {"name": "NLH.Firewall.Range.Internal", "type": "Network", "value": "*************/30", "description": "Migrated from ASA - ", "overridable": false}, {"name": "SSI.EXTERNAL.PEER.1", "type": "Network", "value": "*********/24", "description": "Migrated from ASA - ", "overridable": false}, {"name": "MICROSOFTSTREAM.COM", "type": "FQDN", "value": "MICROSOFTSTREAM.COM", "description": "Migrated from ASA - ", "overridable": false}]}, "service_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects", "method": "POST", "data": [{"name": "obj-tcp-eq-80", "type": "TCPPortObject", "protocol": "TCP", "port": "80", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-15002", "type": "TCPPortObject", "protocol": "TCP", "port": "15002", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-15331", "type": "TCPPortObject", "protocol": "TCP", "port": "15331", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-3389", "type": "TCPPortObject", "protocol": "TCP", "port": "3389", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-2222", "type": "TCPPortObject", "protocol": "TCP", "port": "2222", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-6544", "type": "TCPPortObject", "protocol": "TCP", "port": "6544", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-2020", "type": "TCPPortObject", "protocol": "TCP", "port": "2020", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-23", "type": "TCPPortObject", "protocol": "TCP", "port": "23", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-15031", "type": "TCPPortObject", "protocol": "TCP", "port": "15031", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-5631", "type": "TCPPortObject", "protocol": "TCP", "port": "5631", "description": "Migrated from ASA"}, {"name": "obj-udp-eq-15032", "type": "UDPPortObject", "protocol": "UDP", "port": "15032", "description": "Migrated from ASA"}, {"name": "obj-udp-eq-5632", "type": "UDPPortObject", "protocol": "UDP", "port": "5632", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-25", "type": "TCPPortObject", "protocol": "TCP", "port": "25", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-443", "type": "TCPPortObject", "protocol": "TCP", "port": "443", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-55443", "type": "TCPPortObject", "protocol": "TCP", "port": "55443", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-3401", "type": "TCPPortObject", "protocol": "TCP", "port": "3401", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-53048", "type": "TCPPortObject", "protocol": "TCP", "port": "53048", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-53372", "type": "TCPPortObject", "protocol": "TCP", "port": "53372", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-53050", "type": "TCPPortObject", "protocol": "TCP", "port": "53050", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-53374", "type": "TCPPortObject", "protocol": "TCP", "port": "53374", "description": "Migrated from ASA"}, {"name": "obj-tcp-eq-21", "type": "TCPPortObject", "protocol": "TCP", "port": "21", "description": "Migrated from ASA"}, {"name": "NLI-BG13-FTP", "type": "TCPPortObject", "protocol": "TCP", "port": "1433", "description": "Migrated from ASA"}, {"name": "W32.MYDOOM.OLD", "type": "UDPPortObject", "protocol": "UDP", "port": "3127", "description": "Migrated from ASA"}, {"name": "GREYCASTLE_VPN", "type": "UDPPortObject", "protocol": "UDP", "port": "51820", "description": "Migrated from ASA"}, {"name": "IMO_CLOUD", "type": "TCPPortObject", "protocol": "TCP", "port": "42045", "description": "Migrated from ASA"}, {"name": "NOVA-8070-TCP", "type": "TCPPortObject", "protocol": "TCP", "port": "8070", "description": "Migrated from ASA"}, {"name": "REYHEALTH.EXTERNAL.PORT1", "type": "TCPPortObject", "protocol": "TCP", "port": "18009", "description": "Migrated from ASA"}, {"name": "REYHEALTH.EXTERNAL.PORT2", "type": "TCPPortObject", "protocol": "TCP", "port": "18005", "description": "Migrated from ASA"}, {"name": "NOVA.TOPAZ", "type": "TCPPortObject", "protocol": "TCP", "port": "47290", "description": "Migrated from ASA"}]}, "object_groups": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups", "method": "POST", "data": []}, "access_rules": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/policy/accesspolicies/{policy_uuid}/accessrules", "method": "POST", "data": [{"name": "inside_access_in_rule_1", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_2", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_3", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_4", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_5", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_6", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_7", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_8", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_9", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_10", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_11", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_12", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_13", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_14", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_15", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_16", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_17", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_18", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_19", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_20", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_21", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_22", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_23", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_24", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_25", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_26", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_27", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_28", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_29", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_30", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_31", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_32", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_33", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_34", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_35", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_36", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_37", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_38", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_39", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_40", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_41", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_42", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_43", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_44", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_45", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_46", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_47", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_48", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_49", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_50", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_51", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_52", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_53", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_54", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_55", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_56", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_57", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_58", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_59", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_60", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_61", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_62", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_63", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_64", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_65", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_66", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_67", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_68", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_69", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_70", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_71", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_72", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_73", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_74", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_75", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_76", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_77", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_78", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_79", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_80", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_81", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_82", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_83", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_84", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_85", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_86", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_87", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_88", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_89", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_access_in_rule_90", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_2", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_3", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_4", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_5", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_6", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_7", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_8", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_9", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_10", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_11", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_12", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_13", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_14", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_15", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_16", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_17", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_18", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_19", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_20", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_21", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_22", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_23", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_24", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_25", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_26", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_27", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_28", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_29", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_30", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_31", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_32", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_33", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_34", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_35", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_36", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_37", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_38", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_39", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_40", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_41", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_42", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_43", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_44", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_45", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_46", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_47", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_48", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_49", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_50", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_access_in_rule_51", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_4", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_5", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "DMZ_access_in_V1_rule_6", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_4", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_5", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_6", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_7", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_8", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_9", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_10", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_11", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_12", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_13", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_14", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_15", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_16", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_17", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_18", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_nat0_outbound_rule_19", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "VPN2.nlh.org_splitTunnelAcl_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_6_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "VPN.nlh.org_splitTunnelAcl_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "VPN.nlh.org_splitTunnelAcl_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "VPN.nlh.org_splitTunnelAcl_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_3_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_9_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_10_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_11_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_1_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_12_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_14_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_15_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_2_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_7_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_880_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_pnat_inbound_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V2_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_1000_1_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_13_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_16_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_1120_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V3_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V3_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V3_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V4_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V4_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V4_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_17_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V14_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "inside_pnat_outbound_V15_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_5_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_1300_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "sfr_redirect_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Guest_access_in_rule_1", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Guest_access_in_rule_2", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Guest_access_in_rule_3", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Vendor_access_in_rule_1", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Vendor_access_in_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Vendor_access_in_rule_3", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Vendor_access_in_rule_4", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "Vendor_access_in_rule_5", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_1", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_2", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_3", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_4", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_5", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_6", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_7", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_8", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_9", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_10", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_11", "action": "BLOCK", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_12", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "AnyConnect_Client_Local_Print_rule_13", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_4_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}, {"name": "outside_cryptomap_8_rule_1", "action": "ALLOW", "sourceNetworks": null, "destinationNetworks": null, "sourcePorts": null, "destinationPorts": null, "applications": null, "enabled": true, "logBegin": false, "logEnd": true}]}, "service_groups": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups", "method": "POST", "data": [{"name": "PaceGlobalgrp", "type": "serviceGroup", "objects": [{"value": "6544", "type": "Literal"}, {"value": "2222", "type": "Literal"}, {"value": "2020", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "telnet", "type": "Literal"}, {"value": "53374", "type": "Literal"}, {"value": "53372", "type": "Literal"}, {"value": "53050", "type": "Literal"}, {"value": "53048", "type": "Literal"}]}, {"name": "timeservice", "type": "serviceGroup", "objects": [{"value": "123-123", "type": "Literal"}, {"value": "daytime", "type": "Literal"}]}, {"name": "timeserviceUDP", "type": "serviceGroup", "objects": [{"value": "13", "type": "Literal"}, {"value": "time", "type": "Literal"}, {"value": "ntp", "type": "Literal"}]}, {"name": "QUEST", "type": "serviceGroup", "objects": [{"value": "57010-57013", "type": "Literal"}]}, {"name": "citrixXML", "type": "serviceGroup", "objects": [{"value": "citrix-ica", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "8080", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "GatewayDMZ", "type": "serviceGroup", "objects": [{"value": "citrix-ica", "type": "Literal"}, {"value": "ftp", "type": "Literal"}, {"value": "3389", "type": "Literal"}, {"value": "8080", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "2598", "type": "Literal"}, {"value": "https", "type": "Literal"}, {"value": "ldap", "type": "Literal"}, {"value": "8008", "type": "Literal"}]}, {"name": "RSA", "type": "serviceGroup", "objects": [{"value": "5505-5570", "type": "Literal"}]}, {"name": "HFMBoces", "type": "serviceGroup", "objects": [{"value": "pop3", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "https", "type": "Literal"}, {"value": "smtp", "type": "Literal"}]}, {"name": "GEinbound", "type": "serviceGroup", "objects": [{"value": "3128", "type": "Literal"}, {"value": "8100", "type": "Literal"}, {"value": "4444", "type": "Literal"}, {"value": "ftp-data", "type": "Literal"}, {"value": "ftp", "type": "Literal"}, {"value": "5800", "type": "Literal"}, {"value": "exec", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "2327-2328", "type": "Literal"}, {"value": "3276-3277", "type": "Literal"}, {"value": "8080", "type": "Literal"}, {"value": "5900", "type": "Literal"}, {"value": "telnet", "type": "Literal"}, {"value": "3003", "type": "Literal"}]}, {"name": "GEoutbound", "type": "serviceGroup", "objects": [{"value": "ftp", "type": "Literal"}, {"value": "6000-6200", "type": "Literal"}, {"value": "8002", "type": "Literal"}, {"value": "ftp-data", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "7979", "type": "Literal"}]}, {"name": "PetLinks", "type": "serviceGroup", "objects": [{"value": "21000-21004", "type": "Literal"}, {"value": "16000-16004", "type": "Literal"}, {"value": "8899", "type": "Literal"}]}, {"name": "TeleVideoTcpUdp", "type": "serviceGroup", "objects": [{"value": "3230-3253", "type": "Literal"}, {"value": "1503", "type": "Literal"}, {"value": "1731", "type": "Literal"}, {"value": "1300", "type": "Literal"}, {"value": "1718-1720", "type": "Literal"}]}, {"name": "GEPACS", "type": "serviceGroup", "objects": [{"value": "522", "type": "Literal"}, {"value": "ldap", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "3120", "type": "Literal"}, {"value": "20000", "type": "Literal"}, {"value": "3320", "type": "Literal"}, {"value": "953-999", "type": "Literal"}, {"value": "3389", "type": "Literal"}]}, {"name": "ExchangePorts", "type": "serviceGroup", "objects": [{"value": "https", "type": "Literal"}, {"value": "20443", "type": "Literal"}, {"value": "137", "type": "Literal"}, {"value": "161", "type": "Literal"}, {"value": "9100", "type": "Literal"}]}, {"name": "PrintPorts", "type": "serviceGroup", "objects": [{"value": "lpd", "type": "Literal"}, {"value": "9100", "type": "Literal"}]}, {"name": "PrinterPorts", "type": "serviceGroup", "objects": [{"value": "lpd", "type": "Literal"}, {"value": "9100", "type": "Literal"}]}, {"name": "IPSEC_ISAKMP", "type": "serviceGroup", "objects": [{"value": "isakmp", "type": "Literal"}, {"value": "4500", "type": "Literal"}]}, {"name": "EmdeonPorts", "type": "serviceGroup", "objects": [{"value": "5002", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "in_any_to_out_any_tcp", "type": "serviceGroup", "objects": [{"value": "8080", "type": "Literal"}, {"value": "citrix-ica", "type": "Literal"}, {"value": "1755", "type": "Literal"}, {"value": "ssh", "type": "Literal"}, {"value": "6101-6102", "type": "Literal"}, {"value": "3389", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "https", "type": "Literal"}, {"value": "ftp", "type": "Literal"}, {"value": "ftp-data", "type": "Literal"}, {"value": "9080", "type": "Literal"}, {"value": "netbios-ssn", "type": "Literal"}, {"value": "138", "type": "Literal"}, {"value": "137", "type": "Literal"}, {"value": "55443-55443", "type": "Literal"}, {"value": "25859-25859", "type": "Literal"}, {"value": "8383-8383", "type": "Literal"}, {"value": "telnet", "type": "Literal"}, {"value": "9999-9999", "type": "Literal"}, {"value": "27014-27050", "type": "Literal"}, {"value": "4080-4080", "type": "Literal"}, {"value": "6881-6999", "type": "Literal"}, {"value": "1119", "type": "Literal"}, {"value": "5101", "type": "Literal"}, {"value": "3653", "type": "Literal"}, {"value": "2591", "type": "Literal"}, {"value": "1852-1855", "type": "Literal"}, {"value": "48000-49000", "type": "Literal"}, {"value": "1688-1688", "type": "Literal"}, {"value": "8150-8150", "type": "Literal"}, {"value": "11001-11001", "type": "Literal"}, {"value": "1024-1029", "type": "Literal"}, {"value": "2048", "type": "Literal"}, {"value": "sip-5061", "type": "Literal"}, {"value": "30000-31000", "type": "Literal"}, {"value": "25565", "type": "Literal"}, {"value": "57773", "type": "Literal"}, {"value": "5938", "type": "Literal"}, {"value": "5228", "type": "Literal"}, {"value": "8000-8000", "type": "Literal"}, {"value": "990", "type": "Literal"}, {"value": "1935", "type": "Literal"}, {"value": "5500", "type": "Literal"}, {"value": "50025", "type": "Literal"}, {"value": "65007-65008", "type": "Literal"}, {"value": "6101", "type": "Literal"}, {"value": "900", "type": "Literal"}, {"value": "4080", "type": "Literal"}, {"value": "9443", "type": "Literal"}, {"value": "65009", "type": "Literal"}, {"value": "8443", "type": "Literal"}, {"value": "2443", "type": "Literal"}, {"value": "5494", "type": "Literal"}, {"value": "49210", "type": "Literal"}, {"value": "47290", "type": "Literal"}]}, {"name": "RAMSOFTports", "type": "serviceGroup", "objects": [{"value": "12800-12820", "type": "Literal"}]}, {"name": "CoreFTP", "type": "serviceGroup", "objects": [{"value": "990", "type": "Literal"}, {"value": "1900-1930", "type": "Literal"}]}, {"name": "PhilipsPacs", "type": "serviceGroup", "objects": [{"value": "104", "type": "Literal"}, {"value": "ldap", "type": "Literal"}, {"value": "https", "type": "Literal"}, {"value": "7575", "type": "Literal"}, {"value": "8192", "type": "Literal"}, {"value": "2068", "type": "Literal"}, {"value": "telnet", "type": "Literal"}, {"value": "3211", "type": "Literal"}, {"value": "ssh", "type": "Literal"}, {"value": "6464", "type": "Literal"}, {"value": "1155", "type": "Literal"}, {"value": "www", "type": "Literal"}]}, {"name": "Pacs", "type": "serviceGroup", "objects": [{"value": "6464", "type": "Literal"}, {"value": "https", "type": "Literal"}, {"value": "7575", "type": "Literal"}]}, {"name": "NexTalk1", "type": "serviceGroup", "objects": [{"value": "2591", "type": "Literal"}]}, {"name": "NexTalkTcpUdp", "type": "serviceGroup", "objects": [{"value": "1853", "type": "Literal"}]}, {"name": "CastleSys", "type": "serviceGroup", "objects": [{"value": "9011", "type": "Literal"}, {"value": "9006", "type": "Literal"}]}, {"name": "FTPpsv5500", "type": "serviceGroup", "objects": [{"value": "5500-5700", "type": "Literal"}, {"value": "ftp", "type": "Literal"}]}, {"name": "Labcorp", "type": "serviceGroup", "objects": [{"value": "3611", "type": "Literal"}, {"value": "30032", "type": "Literal"}]}, {"name": "Labcorptcp", "type": "serviceGroup", "objects": [{"value": "ftp", "type": "Literal"}, {"value": "ftp-data", "type": "Literal"}, {"value": "3611", "type": "Literal"}, {"value": "30032", "type": "Literal"}]}, {"name": "IVANStcp", "type": "serviceGroup", "objects": [{"value": "5053", "type": "Literal"}, {"value": "daytime", "type": "Literal"}, {"value": "9920", "type": "Literal"}, {"value": "ldap", "type": "Literal"}, {"value": "709", "type": "Literal"}, {"value": "ftp", "type": "Literal"}, {"value": "5080", "type": "Literal"}, {"value": "3101", "type": "Literal"}]}, {"name": "IVANSudp", "type": "serviceGroup", "objects": [{"value": "isakmp", "type": "Literal"}, {"value": "5081", "type": "Literal"}, {"value": "domain", "type": "Literal"}]}, {"name": "<PERSON>ph<PERSON>", "type": "serviceGroup", "objects": [{"value": "10443", "type": "Literal"}, {"value": "ssh", "type": "Literal"}, {"value": "444", "type": "Literal"}, {"value": "smtp", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "any_in_udp_to_any_out", "type": "serviceGroup", "objects": [{"value": "nameserver", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "domain", "type": "Literal"}, {"value": "time", "type": "Literal"}, {"value": "ntp", "type": "Literal"}, {"value": "netbios-ns", "type": "Literal"}, {"value": "139", "type": "Literal"}, {"value": "netbios-dgm", "type": "Literal"}, {"value": "27000-27015", "type": "Literal"}, {"value": "6881-6999", "type": "Literal"}, {"value": "1119", "type": "Literal"}, {"value": "isakmp", "type": "Literal"}, {"value": "201", "type": "Literal"}, {"value": "4500", "type": "Literal"}, {"value": "sip-5061", "type": "Literal"}, {"value": "1854", "type": "Literal"}, {"value": "1855", "type": "Literal"}, {"value": "1853", "type": "Literal"}, {"value": "1852", "type": "Literal"}, {"value": "30000-31000", "type": "Literal"}, {"value": "34788", "type": "Literal"}]}, {"name": "SophosMail", "type": "serviceGroup", "objects": [{"value": "10443", "type": "Literal"}, {"value": "smtp", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "BobSFTP", "type": "serviceGroup", "objects": [{"value": "21000-21100", "type": "Literal"}]}, {"name": "Impulse.UDP", "type": "serviceGroup", "objects": [{"value": "isakmp", "type": "Literal"}, {"value": "4500", "type": "Literal"}]}, {"name": "ImpulseTCP", "type": "serviceGroup", "objects": [{"value": "51", "type": "Literal"}, {"value": "50", "type": "Literal"}]}, {"name": "TEMP_TRACK1", "type": "serviceGroup", "objects": [{"value": "1001-1001", "type": "Literal"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "serviceGroup", "objects": [{"value": "55443-55443", "type": "Literal"}]}, {"name": "ALLSCRIPT_PORTAL", "type": "serviceGroup", "objects": [{"value": "55443-55443", "type": "Literal"}]}, {"name": "testgroup", "type": "serviceGroup", "objects": [{"value": "8383-8383", "type": "Literal"}]}, {"name": "ALBANYMEDPACS", "type": "serviceGroup", "objects": [{"value": "exec-exec", "type": "Literal"}, {"value": "104-104", "type": "Literal"}]}, {"name": "Guest_Wireless", "type": "serviceGroup", "objects": [{"value": "www", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "SOPHOSFTP", "type": "serviceGroup", "objects": [{"value": "5000-51000", "type": "Literal"}]}, {"name": "BOCES_IPADS", "type": "serviceGroup", "objects": [{"value": "1640", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "5223", "type": "Literal"}, {"value": "2195-2196", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "TEST", "type": "serviceGroup", "objects": [{"value": "9960-9969", "type": "Literal"}, {"value": "29900", "type": "Literal"}, {"value": "18120", "type": "Literal"}, {"value": "18000", "type": "Literal"}, {"value": "1024-1124", "type": "Literal"}, {"value": "18060", "type": "Literal"}, {"value": "28910", "type": "Literal"}, {"value": "27900", "type": "Literal"}]}, {"name": "IMO_Ports", "type": "serviceGroup", "objects": [{"value": "42053", "type": "Literal"}, {"value": "42027", "type": "Literal"}, {"value": "42051", "type": "Literal"}, {"value": "42011", "type": "Literal"}, {"value": "42045", "type": "Literal"}]}, {"name": "TeamViewer", "type": "serviceGroup", "objects": [{"value": "5938", "type": "Literal"}]}, {"name": "CCD_MESSAGING", "type": "serviceGroup", "objects": [{"value": "53374", "type": "Literal"}, {"value": "53050", "type": "Literal"}, {"value": "53372", "type": "Literal"}, {"value": "53048", "type": "Literal"}, {"value": "www", "type": "Literal"}]}, {"name": "Apple_Services", "type": "serviceGroup", "objects": [{"value": "16380-16390", "type": "Literal"}]}, {"name": "ProviderOrg", "type": "serviceGroup", "objects": [{"value": "9443", "type": "Literal"}, {"value": "www", "type": "Literal"}, {"value": "https", "type": "Literal"}]}, {"name": "MAIL_VIRUS", "type": "serviceGroup", "objects": [{"value": "9780", "type": "Literal"}, {"value": "63595", "type": "Literal"}]}, {"name": "STAT_RAD", "type": "serviceGroup", "objects": [{"value": "5406", "type": "Literal"}]}, {"name": "StatRadService", "type": "serviceGroup", "objects": [{"value": "5406", "type": "Literal"}]}, {"name": "PAT_ACCTS_FTP", "type": "serviceGroup", "objects": [{"value": "49730-49750", "type": "Literal"}, {"value": "49000-50000", "type": "Literal"}]}, {"name": "UDP_TEST", "type": "serviceGroup", "objects": [{"value": "59266", "type": "Literal"}, {"value": "51239", "type": "Literal"}]}, {"name": "CE000SVC", "type": "serviceGroup", "objects": [{"value": "8277", "type": "Literal"}]}, {"name": "CE2000", "type": "serviceGroup", "objects": [{"value": "8277", "type": "Literal"}]}, {"name": "m<PERSON>sson", "type": "serviceGroup", "objects": [{"value": "1025-65535", "type": "Literal"}]}, {"name": "DM_INLINE_SERVICE_1", "type": "serviceGroup", "objects": []}, {"name": "MEDENT_TELEMED", "type": "serviceGroup", "objects": [{"value": "1443", "type": "Literal"}, {"value": "3478", "type": "Literal"}]}, {"name": "PHINMS", "type": "serviceGroup", "objects": [{"value": "5088", "type": "Literal"}, {"value": "5089", "type": "Literal"}, {"value": "6087", "type": "Literal"}]}, {"name": "SALUCRO_FTP", "type": "serviceGroup", "objects": [{"value": "20000-21000", "type": "Literal"}]}, {"name": "QUEST_SFTP_NEW", "type": "serviceGroup", "objects": [{"value": "11022", "type": "Literal"}]}, {"name": "REYHEALTH.EXTERNAL.PORT.GROUP", "type": "serviceGroup", "objects": []}, {"name": "DM_INLINE_SERVICE_2", "type": "serviceGroup", "objects": []}]}}, "original_asa_config": {"network_objects": {"RadSaratoga": {"type": "Host", "value": "***********", "name": "RadSaratoga"}, "RadAmsMem": {"type": "Host", "value": "***********", "name": "RadAmsMem"}, "RadStMarys": {"type": "Host", "value": "***********", "name": "RadStMarys"}, "RadSeton": {"type": "Host", "value": "************", "name": "RadSeton"}, "RadBellevue": {"type": "Host", "value": "*************", "name": "RadBellevue"}, "CITRIXFS02": {"type": "Host", "value": "***************", "name": "CITRIXFS02"}, "XENAPP30": {"type": "Host", "value": "***************", "name": "XENAPP30"}, "MAGIC_C-iDRAC": {"type": "Host", "value": "**************0", "name": "MAGIC_C-iDRAC"}, "NLHNAS12": {"type": "Host", "value": "**************8", "name": "NLHNAS12"}, "MAGIC-D_iDRAC": {"type": "Host", "value": "**************5", "name": "MAGIC-D_iDRAC"}, "XENAPP02": {"type": "Host", "value": "***************", "name": "XENAPP02"}, "NLHNAS11": {"type": "Host", "value": "**************9", "name": "NLHNAS11"}, "MAGIC_A-iDRAC": {"type": "Host", "value": "**************7", "name": "MAGIC_A-iDRAC"}, "MAGIC_E-iDRAC": {"type": "Host", "value": "**************2", "name": "MAGIC_E-iDRAC"}, "MAGIC_D-NEWiSCSI": {"type": "Host", "value": "**************3", "name": "MAGIC_D-NEWiSCSI"}, "UNITY-SDC_iSCSI": {"type": "Host", "value": "**************4", "name": "UNITY-SDC_iSCSI"}, "NLH-WEB01-WS01": {"type": "Host", "value": "***************", "name": "NLH-WEB01-WS01"}, "MAGICA": {"type": "Host", "value": "*************", "name": "MAGICA"}, "MAGICC": {"type": "Host", "value": "*************", "name": "MAGICC"}, "MAGICD": {"type": "Host", "value": "*************", "name": "MAGICD"}, "MAGICE": {"type": "Host", "value": "*************", "name": "MAGICE"}, "MAGICB": {"type": "Host", "value": "*************", "name": "MAGICB"}, "MAGICF": {"type": "Host", "value": "*************", "name": "MAGICF"}, "MAGICG": {"type": "Host", "value": "*************", "name": "MAGICG"}, "DICTATION02": {"type": "Host", "value": "*************0", "name": "DICTATION02"}, "MDILIVE.NLH.ORG": {"type": "Host", "value": "***************", "name": "MDILIVE.NLH.ORG"}, "P_FS_SEC": {"type": "Host", "value": "***************", "name": "P_FS_SEC"}, "CTScanner": {"type": "Host", "value": "*************", "name": "CTScanner"}, "Mammo": {"type": "Host", "value": "*************", "name": "<PERSON><PERSON><PERSON>"}, "RandF": {"type": "Host", "value": "*************", "name": "RandF"}, "PetLinks1": {"type": "Host", "value": "**************", "name": "PetLinks1"}, "PetLinks2": {"type": "Host", "value": "**************", "name": "PetLinks2"}, "MAGICD3_DRAC": {"type": "Host", "value": "**************", "name": "MAGICD3_DRAC"}, "LIEBERT.2FL": {"type": "Host", "value": "**************", "name": "LIEBERT.2FL"}, "Cardinal132": {"type": "Host", "value": "*************", "name": "Cardinal132"}, "Cardinal133": {"type": "Host", "value": "*************", "name": "Cardinal133"}, "Cardinal144": {"type": "Host", "value": "*************", "name": "Cardinal144"}, "Cardinal145": {"type": "Host", "value": "*************", "name": "Cardinal145"}, "Cardinal176": {"type": "Host", "value": "*************", "name": "Cardinal176"}, "Cardinal177": {"type": "Host", "value": "*************", "name": "Cardinal177"}, "Cardinal194": {"type": "Host", "value": "*************", "name": "Cardinal194"}, "Cardinal195": {"type": "Host", "value": "*************", "name": "Cardinal195"}, "Medinote": {"type": "Host", "value": "*************", "name": "<PERSON><PERSON><PERSON>"}, "medinote2": {"type": "Host", "value": "**************", "name": "medinote2"}, "medinote1": {"type": "Host", "value": "************", "name": "medinote1"}, "NATHAN3.dmz": {"type": "Host", "value": "***************", "name": "NATHAN3.dmz"}, "NATHAN5.dmz": {"type": "Host", "value": "***************", "name": "NATHAN5.dmz"}, "NATHAN1.dmz": {"type": "Host", "value": "***************", "name": "NATHAN1.dmz"}, "NATHAN4.dmz": {"type": "Host", "value": "***************", "name": "NATHAN4.dmz"}, "NATHAN9.dmz": {"type": "Host", "value": "***************", "name": "NATHAN9.dmz"}, "NATHAN10.dmz": {"type": "Host", "value": "***************", "name": "NATHAN10.dmz"}, "NATHAN11.dmz": {"type": "Host", "value": "***************", "name": "NATHAN11.dmz"}, "MilleniumPACS2": {"type": "Host", "value": "*************", "name": "MilleniumPACS2"}, "MilleniumPACS1": {"type": "Host", "value": "*************", "name": "MilleniumPACS1"}, "MilleniumPACS3": {"type": "Host", "value": "*************", "name": "MilleniumPACS3"}, "MilleniumPACS4": {"type": "Host", "value": "*************", "name": "MilleniumPACS4"}, "MilleniumPACS5": {"type": "Host", "value": "*************", "name": "MilleniumPACS5"}, "NLHEXCHANGE.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHEXCHANGE.NLH.ORG"}, "NLH-ISWEB_VIP_NETSCALER1": {"type": "Host", "value": "**************1", "name": "NLH-ISWEB_VIP_NETSCALER1"}, "TeleMedVT3": {"type": "Network", "value": "************/24", "name": "TeleMedVT3"}, "TelemedVT4": {"type": "Network", "value": "************/24", "name": "TelemedVT4"}, "TelemedVT5": {"type": "Network", "value": "*************/24", "name": "TelemedVT5"}, "TeleMedVT1": {"type": "Network", "value": "************/24", "name": "TeleMedVT1"}, "PACS": {"type": "Host", "value": "***************", "name": "PACS"}, "PACS_CACHE": {"type": "Host", "value": "***************", "name": "PACS_CACHE"}, "PACS_STORE1": {"type": "Host", "value": "***************", "name": "PACS_STORE1"}, "PACS_STORE2": {"type": "Host", "value": "***************", "name": "PACS_STORE2"}, "PACS_STORE144": {"type": "Host", "value": "***************", "name": "PACS_STORE144"}, "ResnickPacs1": {"type": "Host", "value": "***********", "name": "ResnickPacs1"}, "ResnickPACS2": {"type": "Host", "value": "***********", "name": "ResnickPACS2"}, "TeleRadPC": {"type": "Host", "value": "*************", "name": "TeleRadPC"}, "CatScan": {"type": "Host", "value": "**************", "name": "CatScan"}, "PERTH_MRI": {"type": "Host", "value": "**************", "name": "PERTH_MRI"}, "PETScanCT": {"type": "Host", "value": "**************", "name": "PETScanCT"}, "XELERIS": {"type": "Host", "value": "**************", "name": "XELERIS"}, "INFINIA": {"type": "Host", "value": "**************", "name": "INFINIA"}, "D5000": {"type": "Host", "value": "**************", "name": "D5000"}, "Ultrasound1": {"type": "Host", "value": "**************", "name": "Ultrasound1"}, "Ultrasound2": {"type": "Host", "value": "**************", "name": "Ultrasound2"}, "Ultrasound3": {"type": "Host", "value": "**************", "name": "Ultrasound3"}, "KonicaJM": {"type": "Host", "value": "**************", "name": "KonicaJM"}, "Konicardr1": {"type": "Host", "value": "**************", "name": "Konicardr1"}, "KonicaRdr2": {"type": "Host", "value": "**************", "name": "KonicaRdr2"}, "KonicaRdr3": {"type": "Host", "value": "**************", "name": "KonicaRdr3"}, "PACS_NEW": {"type": "Host", "value": "**************", "name": "PACS_NEW"}, "US_LOGI_E9": {"type": "Host", "value": "**************", "name": "US_LOGI_E9"}, "NexTalk242": {"type": "Host", "value": "*************", "name": "NexTalk242"}, "NexTalk243": {"type": "Host", "value": "*************", "name": "NexTalk243"}, "NexTalk244": {"type": "Host", "value": "*************", "name": "NexTalk244"}, "NexTalk245": {"type": "Host", "value": "*************", "name": "NexTalk245"}, "NexTalk246": {"type": "Host", "value": "*************", "name": "NexTalk246"}, "NexTalk247": {"type": "Host", "value": "*************", "name": "NexTalk247"}, "NexTalkPrime": {"type": "Host", "value": "*************", "name": "NexTalkPrime"}, "NexTalkSec": {"type": "Host", "value": "**************", "name": "NexTalkSec"}, "Medent.VPN.net": {"type": "Network", "value": "***********/24", "name": "Medent.VPN.net"}, "Spantel.Prod": {"type": "Host", "value": "*************", "name": "Spantel.Prod"}, "SpantelHL7.test": {"type": "Host", "value": "*************", "name": "SpantelHL7.test"}, "eRXcenter2": {"type": "Host", "value": "**************", "name": "eRXcenter2"}, "eRXcenter3": {"type": "Host", "value": "************", "name": "eRXcenter3"}, "eRXcenter1": {"type": "Host", "value": "*************", "name": "eRXcenter1"}, "eRxChicago": {"type": "Host", "value": "**************", "name": "eRxChicago"}, "eRxDallas": {"type": "Host", "value": "*************", "name": "eRxDallas"}, "MedentRemote": {"type": "Host", "value": "************", "name": "MedentRemote"}, "Medent.RPTS": {"type": "Host", "value": "************", "name": "Medent.RPTS"}, "STpc.rtr": {"type": "Host", "value": "************", "name": "STpc.rtr"}, "spc.rtr": {"type": "Host", "value": "************", "name": "spc.rtr"}, "ppc.pix": {"type": "Host", "value": "***************", "name": "ppc.pix"}, "SMHA.ps1": {"type": "Host", "value": "**********", "name": "SMHA.ps1"}, "SMHA.ps2": {"type": "Host", "value": "**********", "name": "SMHA.ps2"}, "SMHA.ps3": {"type": "Host", "value": "**********", "name": "SMHA.ps3"}, "SMHA.ps4": {"type": "Host", "value": "**********", "name": "SMHA.ps4"}, "SMHA.syn1": {"type": "Host", "value": "***********", "name": "SMHA.syn1"}, "SMHA.syn2": {"type": "Host", "value": "***********", "name": "SMHA.syn2"}, "SMHA.orpc1": {"type": "Host", "value": "***********", "name": "SMHA.orpc1"}, "SMHA.orpc2": {"type": "Host", "value": "***********", "name": "SMHA.orpc2"}, "SMHA.orpc3": {"type": "Host", "value": "***********", "name": "SMHA.orpc3"}, "SMHA.read1": {"type": "Host", "value": "************", "name": "SMHA.read1"}, "SMHA.read2": {"type": "Host", "value": "************", "name": "SMHA.read2"}, "SMHA.read3": {"type": "Host", "value": "************", "name": "SMHA.read3"}, "SMHA.KPServer": {"type": "Host", "value": "************", "name": "SMHA.KPServer"}, "SMHA.read4": {"type": "Host", "value": "***********", "name": "SMHA.read4"}, "smha.mammo": {"type": "Host", "value": "***********", "name": "smha.mammo"}, "smha.pacsed30": {"type": "Host", "value": "***********", "name": "smha.pacsed30"}, "smha.pacrd06": {"type": "Host", "value": "*********", "name": "smha.pacrd06"}, "SMHApacsSUBNET": {"type": "Network", "value": "*************/28", "name": "SMHApacsSUBNET"}, "SMHA.read5": {"type": "Host", "value": "***********", "name": "SMHA.read5"}, "SMHA.read6": {"type": "Host", "value": "***********", "name": "SMHA.read6"}, "SHMA.read7": {"type": "Host", "value": "***********", "name": "SHMA.read7"}, "SMHA.read8": {"type": "Host", "value": "***********", "name": "SMHA.read8"}, "SMHA.read9": {"type": "Host", "value": "************", "name": "SMHA.read9"}, "SMHA.read10": {"type": "Host", "value": "***********", "name": "SMHA.read10"}, "SMHA.Synapse.Dest": {"type": "Host", "value": "**********", "name": "SMHA.Synapse.Dest"}, "P-DI-MGR": {"type": "Host", "value": "**************", "name": "P-DI-MGR"}, "PACS_READ3_NEW": {"type": "Host", "value": "**************", "name": "PACS_READ3_NEW"}, "P_CIO1": {"type": "Host", "value": "**************0", "name": "P_CIO1"}, "P_DI_NUMED": {"type": "Host", "value": "***************", "name": "P_DI_NUMED"}, "MAMMO40": {"type": "Host", "value": "**************", "name": "MAMMO40"}, "MOMMO41": {"type": "Host", "value": "**************", "name": "MOMMO41"}, "philipstst": {"type": "Host", "value": "***************", "name": "phil<PERSON><PERSON><PERSON>"}, "pacs.net": {"type": "Network", "value": "***************/28", "name": "pacs.net"}, "NLHSP19WEB.NLH.ORG": {"type": "Host", "value": "*************9", "name": "NLHSP19WEB.NLH.ORG"}, "PACS_VCE": {"type": "Network", "value": "*************/24", "name": "PACS_VCE"}, "P_PAT_REP1": {"type": "Host", "value": "***************", "name": "P_PAT_REP1"}, "P_PAT_REP5": {"type": "Host", "value": "***************", "name": "P_PAT_REP5"}, "P_PCC_BILL1": {"type": "Host", "value": "***************", "name": "P_PCC_BILL1"}, "P_PAT_REP6": {"type": "Host", "value": "***************", "name": "P_PAT_REP6"}, "P_PAT_REP3": {"type": "Host", "value": "***************", "name": "P_PAT_REP3"}, "P_PAT_REP4": {"type": "Host", "value": "***************", "name": "P_PAT_REP4"}, "SOPHOSEMAIL": {"type": "Host", "value": "**************1", "name": "SOPHOSEMAIL"}, "SOPHOSWEB": {"type": "Host", "value": "**************2", "name": "SOPHOSWEB"}, "NATHAN6.dmz": {"type": "Host", "value": "***************", "name": "NATHAN6.dmz"}, "pacs.net_1": {"type": "Network", "value": "***************/25", "name": "pacs.net_1"}, "ORTIZ_LT": {"type": "Host", "value": "**************", "name": "ORTIZ_LT"}, "p_mis_netadmin": {"type": "Host", "value": "***************", "name": "p_mis_netadmin"}, "PACS_OR3": {"type": "Host", "value": "**************", "name": "PACS_OR3"}, "PACS_OR1": {"type": "Host", "value": "***************", "name": "PACS_OR1"}, "PACS_OR2": {"type": "Host", "value": "**************", "name": "PACS_OR2"}, "PACS_DI": {"type": "Host", "value": "***************", "name": "PACS_DI"}, "NLHDC1_IPMI": {"type": "Host", "value": "**************8", "name": "NLHDC1_IPMI"}, "NLHDC2_IPMI": {"type": "Host", "value": "**************9", "name": "NLHDC2_IPMI"}, "AAI.120": {"type": "Host", "value": "***********", "name": "AAI.120"}, "AAI.124": {"type": "Host", "value": "***********", "name": "AAI.124"}, "AAI.125": {"type": "Host", "value": "***********", "name": "AAI.125"}, "AAI.52": {"type": "Host", "value": "***********", "name": "AAI.52"}, "INTERLACE": {"type": "Host", "value": "*************8", "name": "INTERLACE"}, "NLHUTILITY": {"type": "Host", "value": "*************7", "name": "NLHUTILITY"}, "NLHTEST01-NIC2": {"type": "Host", "value": "*************51", "name": "NLHTEST01-NIC2"}, "BPC-UPS": {"type": "Host", "value": "**************", "name": "BPC-UPS"}, "ALBANYMED.IN.2": {"type": "Host", "value": "*************", "name": "ALBANYMED.IN.2"}, "ALBANYMED.IN": {"type": "Host", "value": "***************", "name": "ALBANYMED.IN"}, "hixny.com_integration": {"type": "Host", "value": "************", "name": "hixny.com_integration"}, "hixny.com_prod": {"type": "Host", "value": "************", "name": "hixny.com_prod"}, "webservices.hixny.com": {"type": "Host", "value": "*************", "name": "webservices.hixny.com"}, "Olympus.Inside.New": {"type": "Network", "value": "***********/24", "name": "Olympus.Inside.New"}, "MDITEST": {"type": "Host", "value": "**************", "name": "MDITEST"}, "MEDENT": {"type": "Host", "value": "***************", "name": "MEDENT"}, "MEDENT03": {"type": "Host", "value": "**************", "name": "MEDENT03"}, "NLH-ISWEB_VIRTUALIP": {"type": "Host", "value": "**************2", "name": "NLH-ISWEB_VIRTUALIP"}, "NLH-ISWEB_VIRTUALIP_NETSCALER2": {"type": "Host", "value": "**************5", "name": "NLH-ISWEB_VIRTUALIP_NETSCALER2"}, "MEDENT05": {"type": "Host", "value": "**************", "name": "MEDENT05"}, "P_PHA_WS3": {"type": "Host", "value": "**************", "name": "P_PHA_WS3"}, "P_PHA_WS2": {"type": "Host", "value": "**************", "name": "P_PHA_WS2"}, "P_MR_SCAN1": {"type": "Host", "value": "***************", "name": "P_MR_SCAN1"}, "easyeeg": {"type": "Host", "value": "***************", "name": "easyeeg"}, "VENUE50_p_pacs_cdburn": {"type": "Host", "value": "**************", "name": "VENUE50_p_pacs_cdburn"}, "integration.hixny.com": {"type": "Host", "value": "*************", "name": "integration.hixny.com"}, "Hixney.net_2": {"type": "Host", "value": "*************", "name": "Hixney.net_2"}, "CITRIX_STOREFRONT": {"type": "Host", "value": "*************53", "name": "CITRIX_STOREFRONT"}, "P-IT-MGR": {"type": "Host", "value": "***************", "name": "P-IT-MGR"}, "NETSCALER.VPX": {"type": "Host", "value": "***************", "name": "NETSCALER.VPX"}, "NETSCALER.WEB": {"type": "Host", "value": "***************", "name": "NETSCALER.WEB"}, "NETSCALERSUBNETIP": {"type": "Host", "value": "***************", "name": "NETSCALERSUBNETIP"}, "NLHDC01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHDC01.NLH.ORG"}, "NLHDC02.NLH.ORG": {"type": "Host", "value": "*************13", "name": "NLHDC02.NLH.ORG"}, "P_CISCO_01": {"type": "Host", "value": "***************", "name": "P_CISCO_01"}, "p_mis_netadmin2": {"type": "Host", "value": "***************", "name": "p_mis_netadmin2"}, "Direct.Hixny.Com": {"type": "Host", "value": "************", "name": "Direct.Hixny.Com"}, "Healthstream.SMPT.Peer": {"type": "Host", "value": "*************", "name": "Healthstream.SMPT.Peer"}, "Hixny.net": {"type": "Host", "value": "***************", "name": "Hixny.net"}, "Hixny.com": {"type": "Host", "value": "************7", "name": "Hixny.com"}, "statrad.hl7.test": {"type": "Host", "value": "***************", "name": "statrad.hl7.test"}, "P_PAT_FIN": {"type": "Host", "value": "***************", "name": "P_PAT_FIN"}, "NLHENDO01": {"type": "Host", "value": "*************5", "name": "NLHENDO01"}, "NLHENDO01.NLH.ORG": {"type": "Host", "value": "*************0", "name": "NLHENDO01.NLH.ORG"}, "ENDOWORKS02": {"type": "Host", "value": "*************1", "name": "ENDOWORKS02"}, "ENDOWORKS03": {"type": "Host", "value": "*************2", "name": "ENDOWORKS03"}, "P_IS_PACS": {"type": "Host", "value": "***************", "name": "P_IS_PACS"}, "retsolinc2.com": {"type": "Host", "value": "**************", "name": "retsolinc2.com"}, "retsolinc3.com": {"type": "Host", "value": "************", "name": "retsolinc3.com"}, "SophosMailExt": {"type": "Host", "value": "**************", "name": "SophosMailExt"}, "speculator": {"type": "Network", "value": "***************/27", "name": "speculator"}, "GEserviceNET": {"type": "Network", "value": "*********/16", "name": "GEserviceNET"}, "IRIS": {"type": "Host", "value": "*********", "name": "IRIS"}, "Mill.PACS.NET": {"type": "Network", "value": "**********/16", "name": "Mill.PACS.NET"}, "smtp.biz.rr.com": {"type": "Host", "value": "*************", "name": "smtp.biz.rr.com"}, "Hypertype": {"type": "Host", "value": "************", "name": "Hypertype"}, "MVP": {"type": "Host", "value": "************", "name": "MVP"}, "LeaderHFTPsite": {"type": "Host", "value": "************", "name": "LeaderHFTPsite"}, "LeaderHFTPsite2": {"type": "Host", "value": "**************", "name": "LeaderHFTPsite2"}, "stentor.com": {"type": "Host", "value": "*************", "name": "stentor.com"}, "TOGARM": {"type": "Host", "value": "************", "name": "TOGARM"}, "Infotrak": {"type": "Host", "value": "***************", "name": "Infotrak"}, "sftp.lifethc.org": {"type": "Host", "value": "***************", "name": "sftp.lifethc.org"}, "DI.NET": {"type": "Network", "value": "*************/25", "name": "DI.NET"}, "TeleVideo1": {"type": "Host", "value": "***************", "name": "TeleVideo1"}, "Televid2": {"type": "Host", "value": "***************", "name": "Televid2"}, "STUDENT_VLAN": {"type": "Network", "value": "*********/24", "name": "STUDENT_VLAN"}, "CONNECTPLUS01": {"type": "Host", "value": "*************3", "name": "CONNECTPLUS01"}, "VeriquestPC": {"type": "Host", "value": "**************9", "name": "VeriquestPC"}, "VeriquestSite": {"type": "Host", "value": "***********", "name": "VeriquestSite"}, "PATIENT_PORTAL_1": {"type": "Host", "value": "**************", "name": "PATIENT_PORTAL_1"}, "questlab": {"type": "Network", "value": "*************/25", "name": "questlab"}, "HYPER-_REPLICA_BROKER": {"type": "Host", "value": "*************6", "name": "HYPER-_REPLICA_BROKER"}, "iPEOPLEremote": {"type": "Network", "value": "************/24", "name": "iPEOPLEremote"}, "Sodexho": {"type": "Host", "value": "*************", "name": "Sodexho"}, "Provation-out": {"type": "Host", "value": "**************", "name": "Provation-out"}, "VeriquestServer": {"type": "Host", "value": "**************", "name": "VeriquestServer"}, "LAN": {"type": "Network", "value": "*************/20", "name": "LAN"}, "Harland": {"type": "Host", "value": "************", "name": "<PERSON><PERSON>"}, "IMO_2": {"type": "Host", "value": "**************", "name": "IMO_2"}, "WWW.UPTODATE.COM": {"type": "Host", "value": "************", "name": "WWW.UPTODATE.COM"}, "XENAPP22": {"type": "Host", "value": "**************", "name": "XENAPP22"}, "HYPER-V_CLUSTER": {"type": "Host", "value": "*************2", "name": "HYPER-V_CLUSTER"}, "PATIENTPORTAL.EXTERNAL": {"type": "Host", "value": "**************", "name": "PATIENTPORTAL.EXTERNAL"}, "remote.nlh.org": {"type": "Host", "value": "**************", "name": "remote.nlh.org"}, "mail.nlh.org": {"type": "Host", "value": "**************", "name": "mail.nlh.org"}, "DIRECT.NLH.ORG": {"type": "Host", "value": "**************", "name": "DIRECT.NLH.ORG"}, "TeleMed_1": {"type": "Host", "value": "**************", "name": "TeleMed_1"}, "MDI.dmz": {"type": "Host", "value": "***************", "name": "MDI.dmz"}, "NLHCISCO": {"type": "Host", "value": "*************7", "name": "NLHCISCO"}, "RALSplusLAN": {"type": "Network", "value": "*************/24", "name": "RALSplusLAN"}, "PhilipsSupport": {"type": "Network", "value": "***********/25", "name": "PhilipsSupport"}, "STRAT_SOL.NET.INTERNAL1": {"type": "Network", "value": "***********/24", "name": "STRAT_SOL.NET.INTERNAL1"}, "MVOrtho.net": {"type": "Network", "value": "***********/24", "name": "MVOrtho.net"}, "LAN_1": {"type": "Network", "value": "*************/23", "name": "LAN_1"}, "LabCorp3": {"type": "Host", "value": "*************", "name": "LabCorp3"}, "LabCorpDev": {"type": "Host", "value": "************", "name": "LabCorpDev"}, "LabCorpProd": {"type": "Host", "value": "************", "name": "LabCorpProd"}, "MilleniumPACSnat": {"type": "Network", "value": "***********/24", "name": "MilleniumPACSnat"}, "TheOutsourceGroup": {"type": "Host", "value": "************", "name": "TheOutsourceGroup"}, "TeleradIT_Millenium1": {"type": "Host", "value": "**************", "name": "TeleradIT_Millenium1"}, "TeleradIT_Millenium2": {"type": "Host", "value": "**************", "name": "TeleradIT_Millenium2"}, "MVOatJSC.net": {"type": "Network", "value": "**********/24", "name": "MVOatJSC.net"}, "SENTRYDS.NET": {"type": "Network", "value": "***********/29", "name": "SENTRYDS.NET"}, "FastChart.Inside": {"type": "Host", "value": "************", "name": "FastChart.Inside"}, "Ellis.inside": {"type": "Host", "value": "**********", "name": "Ellis.inside"}, "STATRAD.DR.SVR": {"type": "Host", "value": "*************", "name": "STATRAD.DR.SVR"}, "SENTRYDS": {"type": "Network", "value": "***************/28", "name": "SENTRYDS"}, "NLHTEST01": {"type": "Host", "value": "***************", "name": "NLHTEST01"}, "NLH.ORG.EXTERNAL.FORMS": {"type": "Host", "value": "**************", "name": "NLH.ORG.EXTERNAL.FORMS"}, "pacs.net-01": {"type": "Network", "value": "***************/25", "name": "pacs.net-01"}, "LAN-01": {"type": "Network", "value": "*************/23", "name": "LAN-01"}, "obj-************": {"type": "Host", "value": "************", "name": "obj-************"}, "obj-***********": {"type": "Host", "value": "***********", "name": "obj-***********"}, "obj-***********": {"type": "Host", "value": "***********", "name": "obj-***********"}, "obj-************": {"type": "Host", "value": "************", "name": "obj-************"}, "obj-***********": {"type": "Host", "value": "***********", "name": "obj-***********"}, "obj-************": {"type": "Host", "value": "************", "name": "obj-************"}, "MilleniumPACSnat-*************": {"type": "Range", "value": "***********-*************", "name": "MilleniumPACSnat-*************"}, "obj-************": {"type": "Host", "value": "************", "name": "obj-************"}, "obj-*************-*************": {"type": "Range", "value": "*************-*************", "name": "obj-*************-*************"}, "obj_any": {"type": "Network", "value": "0.0.0.0/0", "name": "obj_any"}, "Ellis.Peer.New": {"type": "Host", "value": "**********", "name": "Ellis.Peer.New"}, "HEALTHTOUCH.PEER.INTERNAL.1": {"type": "Host", "value": "************", "name": "HEALTHTOUCH.PEER.INTERNAL.1"}, "Medent.Peer.New.": {"type": "Host", "value": "*************", "name": "Medent.Peer.New."}, "HIXNY.MBMS.MILLENIUMBILLING.PEER": {"type": "Host", "value": "***********", "name": "HIXNY.MBMS.MILLENIUMBILLING.PEER"}, "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1": {"type": "Host", "value": "***************", "name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1"}, "MCKESSON.MC.PHARM": {"type": "Host", "value": "**************", "name": "MCKESSON.MC.PHARM"}, "newsync3.mkesson.com": {"type": "Host", "value": "**************", "name": "newsync3.mkesson.com"}, "obj-0.0.0.0": {"type": "Host", "value": "0.0.0.0", "name": "obj-0.0.0.0"}, "obj_any-03": {"type": "Network", "value": "0.0.0.0/0", "name": "obj_any-03"}, "SMHA.pacs1": {"type": "Host", "value": "**********", "name": "SMHA.pacs1"}, "SMHA.pacs2": {"type": "Host", "value": "***********", "name": "SMHA.pacs2"}, "SMHA.pacs3": {"type": "Host", "value": "**********", "name": "SMHA.pacs3"}, "PACS.VCE1": {"type": "Host", "value": "**************", "name": "PACS.VCE1"}, "PACS.VCE2": {"type": "Host", "value": "***************", "name": "PACS.VCE2"}, "PACS.VCE3": {"type": "Host", "value": "***************", "name": "PACS.VCE3"}, "PACS.VCE4": {"type": "Host", "value": "***************", "name": "PACS.VCE4"}, "MEDENT_NAS_INTERNAL": {"type": "Host", "value": "***************", "name": "MEDENT_NAS_INTERNAL"}, "HEALTHTOUCH01": {"type": "Host", "value": "**************", "name": "HEALTHTOUCH01"}, "HEALTHTOUCH02": {"type": "Host", "value": "**************", "name": "HEALTHTOUCH02"}, "PDX.Internal": {"type": "Host", "value": "**************", "name": "PDX.Internal"}, "PDX.External": {"type": "Host", "value": "*************", "name": "PDX.External"}, "HIXNY.PEER.NEW": {"type": "Host", "value": "**************", "name": "HIXNY.PEER.NEW"}, "HIXNY.INTERNAL1": {"type": "Host", "value": "************", "name": "HIXNY.INTERNAL1"}, "XCHANGEWORX.PEER": {"type": "Host", "value": "**************", "name": "XCHANGEWORX.PEER"}, "NETSCALER.NLHRESTAPI": {"type": "Host", "value": "***************", "name": "NETSCALER.NLHRESTAPI"}, "NUVODIA_NETWORK_3": {"type": "Network", "value": "*************/29", "name": "NUVODIA_NETWORK_3"}, "NUVODIA_VPN_NLH_PEER1": {"type": "Host", "value": "**************", "name": "NUVODIA_VPN_NLH_PEER1"}, "NUVODIA_VPN_NLH_PEER2": {"type": "Host", "value": "**************", "name": "NUVODIA_VPN_NLH_PEER2"}, "FIREPOWER_VM_ESXI": {"type": "Host", "value": "**************", "name": "FIREPOWER_VM_ESXI"}, "SMHA.READ.10": {"type": "Host", "value": "***********", "name": "SMHA.READ.10"}, "ESRS_EMC_VIRTUAL_APPLIANCE": {"type": "Host", "value": "**************", "name": "ESRS_EMC_VIRTUAL_APPLIANCE"}, "NLH-ISWEB.INTERNAL": {"type": "Host", "value": "**************", "name": "NLH-ISWEB.INTERNAL"}, "NLH-ISWEB.DMZ": {"type": "Host", "value": "***************", "name": "NLH-ISWEB.DMZ"}, "RESTFULAPI.DMZ": {"type": "Host", "value": "***************", "name": "RESTFULAPI.DMZ"}, "NYOH.INTERNAL.1": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.1"}, "NYOH.INTERNAL.2": {"type": "Host", "value": "************", "name": "NYOH.INTERNAL.2"}, "NYOH.EXTERNAL.PEER": {"type": "Host", "value": "**************", "name": "NYOH.EXTERNAL.PEER"}, "NUVODIA.INTERNAL.1": {"type": "Host", "value": "**************", "name": "NUVODIA.INTERNAL.1"}, "NUVODIA.INTERNAL.2": {"type": "Host", "value": "**************", "name": "NUVODIA.INTERNAL.2"}, "P_MIS52_DMZ": {"type": "Host", "value": "***************", "name": "P_MIS52_DMZ"}, "MDITEST_SENDTRYDS": {"type": "Host", "value": "***********", "name": "MDITEST_SENDTRYDS"}, "XENAPP25": {"type": "Host", "value": "*************3", "name": "XENAPP25"}, "skype.nlh.org_external": {"type": "Host", "value": "**************", "name": "skype.nlh.org_external"}, "st_netadmin": {"type": "Host", "value": "***************", "name": "st_netadmin"}, "SMHA.RAD.EXTERNAL": {"type": "Host", "value": "***********", "name": "SMHA.RAD.EXTERNAL"}, "MVO_AMST_PEER_NEW": {"type": "Host", "value": "***********", "name": "MVO_AMST_PEER_NEW"}, "GUEST_INTERFACE_EXTERNAL": {"type": "Host", "value": "**************", "name": "GUEST_INTERFACE_EXTERNAL"}, "GUEST_WLAN_NAT": {"type": "Network", "value": "**********/24", "name": "GUEST_WLAN_NAT"}, "GUEST_NETWORK": {"type": "Network", "value": "**********/24", "name": "GUEST_NETWORK"}, "VENDOR_WLAN_NAT": {"type": "Network", "value": "**********/24", "name": "VENDOR_WLAN_NAT"}, "VENDOR_EXTERNAL_INTERFACE": {"type": "Host", "value": "**************", "name": "VENDOR_EXTERNAL_INTERFACE"}, "p_mis_netadmin.dmz": {"type": "Host", "value": "***************", "name": "p_mis_netadmin.dmz"}, "NLH-ISWEB.DMZVR": {"type": "Host", "value": "***************", "name": "NLH-ISWEB.DMZVR"}, "CREDITCARD_CAFE_EXTERNAL1": {"type": "Network", "value": "*************/28", "name": "CREDITCARD_CAFE_EXTERNAL1"}, "CREDITCARD_CAFE2_EXTERNAL2": {"type": "Network", "value": "*************/27", "name": "CREDITCARD_CAFE2_EXTERNAL2"}, "CREDITCARD_CAFE_EXTERNAL": {"type": "Network", "value": "*************/27", "name": "CREDITCARD_CAFE_EXTERNAL"}, "AMC.PACS.NEW": {"type": "Host", "value": "***********", "name": "AMC.PACS.NEW"}, "BRIAN_DHCP": {"type": "Host", "value": "**************", "name": "BRIAN_DHCP"}, "BPC.External": {"type": "Host", "value": "**************", "name": "BPC.External"}, "STRAT_SOL.NET.INTERNAL2": {"type": "Network", "value": "***********/24", "name": "STRAT_SOL.NET.INTERNAL2"}, "P_MIS_CISCOMON": {"type": "Host", "value": "***************", "name": "P_MIS_CISCOMON"}, "XENAPP17": {"type": "Host", "value": "**************", "name": "XENAPP17"}, "XENAPP18": {"type": "Host", "value": "**************", "name": "XENAPP18"}, "XENAPP19": {"type": "Host", "value": "**************", "name": "XENAPP19"}, "P_MIS52.WAYNE": {"type": "Host", "value": "***************", "name": "P_MIS52.WAYNE"}, "NETADMIN.DMZ.TEST": {"type": "Host", "value": "***************", "name": "NETADMIN.DMZ.TEST"}, "EUGENE10": {"type": "Host", "value": "***************", "name": "EUGENE10"}, "MEDITECHAPIVIP1": {"type": "Host", "value": "***************", "name": "MEDITECHAPIVIP1"}, "MEDITECHAPIVIP2": {"type": "Host", "value": "***************", "name": "MEDITECHAPIVIP2"}, "StratSolution.Peer": {"type": "Host", "value": "************", "name": "StratSolution.Peer"}, "P_PHA_PDX1": {"type": "Host", "value": "***************", "name": "P_PHA_PDX1"}, "NLHPRTG01": {"type": "Host", "value": "**************", "name": "NLHPRTG01"}, "RCARE-SERVER": {"type": "Host", "value": "**************", "name": "RCARE-SERVER"}, "NLHDMZ01_SWITCH": {"type": "Host", "value": "***************", "name": "NLHDMZ01_SWITCH"}, "XENAPP01": {"type": "Host", "value": "**************", "name": "XENAPP01"}, "PRTG.NLH.ORG.EXTERNAL": {"type": "Host", "value": "**************", "name": "PRTG.NLH.ORG.EXTERNAL"}, "BACKLINE.VPN.PEER": {"type": "Host", "value": "**************", "name": "BACKLINE.VPN.PEER"}, "UNITEDLABNETWORK.VPN.PEER": {"type": "Host", "value": "**************", "name": "UNITEDLABNETWORK.VPN.PEER"}, "NETWORK_OBJ_**************": {"type": "Host", "value": "**************", "name": "NETWORK_OBJ_**************"}, "BACKLINE.LDAP.INTERNAL": {"type": "Host", "value": "**************", "name": "BACKLINE.LDAP.INTERNAL"}, "MEDENT.NIMBLE.INSIDE.1": {"type": "Host", "value": "**************", "name": "MEDENT.NIMBLE.INSIDE.1"}, "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1": {"type": "Host", "value": "*************", "name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1"}, "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2": {"type": "Host", "value": "***********", "name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2"}, "ROBOT_GE_VOT_TRAIN": {"type": "Host", "value": "**************", "name": "ROBOT_GE_VOT_TRAIN"}, "BILL_BAIRD": {"type": "Host", "value": "*************", "name": "BILL_BAIRD"}, "EXPANSE_VLAN1": {"type": "Network", "value": "**********/24", "name": "EXPANSE_VLAN1"}, "EXPANSE_VLAN2": {"type": "Network", "value": "**********/24", "name": "EXPANSE_VLAN2"}, "EXPANSE_VLAN3": {"type": "Network", "value": "**********/24", "name": "EXPANSE_VLAN3"}, "EXPANSE_VLAN4": {"type": "Network", "value": "**********/24", "name": "EXPANSE_VLAN4"}, "LUCIUS29-iDRAC": {"type": "Host", "value": "**************", "name": "LUCIUS29-iDRAC"}, "DOLBEY": {"type": "Host", "value": "***************", "name": "DOLBEY"}, "DOLBEYTEST": {"type": "Host", "value": "*************0", "name": "DOLBEYTEST"}, "NLH_DCDS_9300s": {"type": "Host", "value": "***************", "name": "NLH_DCDS_9300s"}, "Schumacher.Inside1.new.ADTPROD": {"type": "Host", "value": "*************", "name": "Schumacher.Inside1.new.ADTPROD"}, "Schumacher.Inside2.new.ADTTEST": {"type": "Host", "value": "*************", "name": "Schumacher.Inside2.new.ADTTEST"}, "Schumacher.VPN.Peer.New": {"type": "Host", "value": "*************0", "name": "Schumacher.VPN.Peer.New"}, "MEDENT-EXPORT": {"type": "Host", "value": "**************", "name": "MEDENT-EXPORT"}, "QUEST.VPN.PEER.2": {"type": "Host", "value": "************", "name": "QUEST.VPN.PEER.2"}, "QUEST.VPN.EXTERNAL.2": {"type": "Network", "value": "************/28", "name": "QUEST.VPN.EXTERNAL.2"}, "QUEST.VPN.INTERNAL.2": {"type": "Host", "value": "***********", "name": "QUEST.VPN.INTERNAL.2"}, "Wayne": {"type": "Host", "value": "***************", "name": "<PERSON>"}, "HIXNY.PEER.INTERNAL.TEST": {"type": "Host", "value": "************", "name": "HIXNY.PEER.INTERNAL.TEST"}, "HIXNY.PEER.INTERNAL.PROD": {"type": "Host", "value": "************", "name": "HIXNY.PEER.INTERNAL.PROD"}, "NLHSP19OFCWEB.NLH.ORG": {"type": "Host", "value": "**************8", "name": "NLHSP19OFCWEB.NLH.ORG"}, "PATIENTPORTAL.DMZ": {"type": "Host", "value": "**************", "name": "PATIENTPORTAL.DMZ"}, "mtrestexpapis-live01.nlh.org.external": {"type": "Host", "value": "**************", "name": "mtrestexpapis-live01.nlh.org.external"}, "mtrestexpapis-test01.nlh.org.external": {"type": "Host", "value": "**************", "name": "mtrestexpapis-test01.nlh.org.external"}, "mtrestexpapis-test01.nlh.org.DMZ": {"type": "Host", "value": "**************", "name": "mtrestexpapis-test01.nlh.org.DMZ"}, "mtrestexpapis-live01.nlh.org.DMZ": {"type": "Host", "value": "**************", "name": "mtrestexpapis-live01.nlh.org.DMZ"}, "CHANGE.HEALTHCARE.EXTERNAL.PEER": {"type": "Host", "value": "*************", "name": "CHANGE.HEALTHCARE.EXTERNAL.PEER"}, "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD": {"type": "Host", "value": "***************", "name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD"}, "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST": {"type": "Host", "value": "***************", "name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST"}, "NLI.T.BG01": {"type": "Host", "value": "***********", "name": "NLI.T.BG01"}, "CHC.EXTERNAL.1": {"type": "Host", "value": "***************", "name": "CHC.EXTERNAL.1"}, "CHC.EXTERNAL.2": {"type": "Host", "value": "***************", "name": "CHC.EXTERNAL.2"}, "NLI-T-BG01.CHC.NAT": {"type": "Host", "value": "************", "name": "NLI-T-BG01.CHC.NAT"}, "MDILIVE.CHC.NAT": {"type": "Host", "value": "************", "name": "MDILIVE.CHC.NAT"}, "MDITEST.CHC.NAT": {"type": "Host", "value": "************", "name": "MDITEST.CHC.NAT"}, "NLI-T-BG01": {"type": "Host", "value": "***********", "name": "NLI-T-BG01"}, "NLHFTP01.NLH.ORG": {"type": "Host", "value": "**************4", "name": "NLHFTP01.NLH.ORG"}, "SR_STACK_01": {"type": "Host", "value": "**************", "name": "SR_STACK_01"}, "NLI-BG01.nlh.org": {"type": "Host", "value": "***********", "name": "NLI-BG01.nlh.org"}, "NLI-BG04.CHC.NAT": {"type": "Host", "value": "************", "name": "NLI-BG04.CHC.NAT"}, "CHC.OPTUM.NAT.INTERNAL.SUB": {"type": "Range", "value": "***************-***************", "name": "CHC.OPTUM.NAT.INTERNAL.SUB"}, "NLI-BG04": {"type": "Host", "value": "***********", "name": "NLI-BG04"}, "CHC.EXTERNAL.3": {"type": "Host", "value": "***************", "name": "CHC.EXTERNAL.3"}, "NYOH.INTERNAL.3": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.3"}, "WEBSSO.MEDITECH.COM": {"type": "Host", "value": "**************", "name": "WEBSSO.MEDITECH.COM"}, "WEBSSO2FA.MEDITECH.COM": {"type": "Host", "value": "*************", "name": "WEBSSO2FA.MEDITECH.COM"}, "HIXNY.INTERNAL.PUSH_SERVER": {"type": "Host", "value": "************", "name": "HIXNY.INTERNAL.PUSH_SERVER"}, "HIXNY.INTERNAL.TESTHUB": {"type": "Host", "value": "************", "name": "HIXNY.INTERNAL.TESTHUB"}, "FIRECALL_JSC": {"type": "Host", "value": "**************", "name": "FIRECALL_JSC"}, "FIRECALLSYSTEM_ENDPOINTS1": {"type": "Host", "value": "************", "name": "FIRECALLSYSTEM_ENDPOINTS1"}, "FIRECALLSYSTEM_ENDPOINTS2": {"type": "Host", "value": "************", "name": "FIRECALLSYSTEM_ENDPOINTS2"}, "BACKLINE.VPN.PEER2": {"type": "Host", "value": "************", "name": "BACKLINE.VPN.PEER2"}, "BACKLINE.LDAP.INTERNAL2": {"type": "Host", "value": "*************", "name": "BACKLINE.LDAP.INTERNAL2"}, "NETWORK_OBJ_*************": {"type": "Host", "value": "*************", "name": "NETWORK_OBJ_*************"}, "BANDWIDTH_TEST": {"type": "Host", "value": "***************", "name": "BANDWIDTH_TEST"}, "P_IS_1": {"type": "Host", "value": "**************", "name": "P_IS_1"}, "P_IS_3": {"type": "Host", "value": "**************", "name": "P_IS_3"}, "P_IT_COOR": {"type": "Host", "value": "***************", "name": "P_IT_COOR"}, "P_IT_TECH1": {"type": "Host", "value": "*************", "name": "P_IT_TECH1"}, "HIRAM": {"type": "Host", "value": "**************", "name": "HIRAM"}, "RYAN": {"type": "Host", "value": "**************", "name": "RYAN"}, "NICK": {"type": "Host", "value": "**************", "name": "NICK"}, "IT_TEST": {"type": "Host", "value": "**************6", "name": "IT_TEST"}, "P-BOARDROOM1": {"type": "Host", "value": "**************", "name": "P-BOARDROOM1"}, "LUCIUS08": {"type": "Host", "value": "*************", "name": "LUCIUS08"}, "LUCIUS21-iLO": {"type": "Host", "value": "*************", "name": "LUCIUS21-iLO"}, "NLHADMINCENTER": {"type": "Host", "value": "**************", "name": "NLHADMINCENTER"}, "XENAPP24": {"type": "Host", "value": "*************9", "name": "XENAPP24"}, "SQL01.NLH.ORG": {"type": "Host", "value": "*************3", "name": "SQL01.NLH.ORG"}, "FAXSERVER.NLH.ORG": {"type": "Host", "value": "*************5", "name": "FAXSERVER.NLH.ORG"}, "NLHFUSION": {"type": "Host", "value": "**************", "name": "NLHFUSION"}, "BACKUPEXEC01": {"type": "Host", "value": "**************", "name": "BACKUPEXEC01"}, "ARCHIVE.NLH.ORG": {"type": "Host", "value": "**************", "name": "ARCHIVE.NLH.ORG"}, "PRINT": {"type": "Host", "value": "**************", "name": "PRINT"}, "NLHBACKUP": {"type": "Host", "value": "**************", "name": "NLHBACKUP"}, "INTERLACETEST": {"type": "Host", "value": "**************", "name": "INTERLACETEST"}, "NLHMONITOR01": {"type": "Host", "value": "**************", "name": "NLHMONITOR01"}, "SANPHNHM": {"type": "Host", "value": "**************", "name": "SANPHNHM"}, "CENTRALINK_BCR": {"type": "Host", "value": "**************", "name": "CENTRALINK_BCR"}, "CENTRALINK_VISTA2": {"type": "Host", "value": "**************", "name": "CENTRALINK_VISTA2"}, "CENTRALINK_VISTA1": {"type": "Host", "value": "**************", "name": "CENTRALINK_VISTA1"}, "CENTRALINK_LCM": {"type": "Host", "value": "**************", "name": "CENTRALINK_LCM"}, "CENTRALINK": {"type": "Host", "value": "**************", "name": "CENTRALINK"}, "LUCIUS31-iDRAC": {"type": "Host", "value": "**************", "name": "LUCIUS31-iDRAC"}, "XENAPP21": {"type": "Host", "value": "***************", "name": "XENAPP21"}, "NLHCITRIXGATEWAY": {"type": "Host", "value": "***************", "name": "NLHCITRIXGATEWAY"}, "ST_NETADMIN2": {"type": "Host", "value": "***************", "name": "ST_NETADMIN2"}, "DR_CECIL": {"type": "Host", "value": "***************", "name": "DR_CECIL"}, "P_IS_RAMANI": {"type": "Host", "value": "***************", "name": "P_IS_RAMANI"}, "US_LOGU_E9_2": {"type": "Host", "value": "**************", "name": "US_LOGU_E9_2"}, "NYOH.INTERNAL.4": {"type": "Host", "value": "**************", "name": "NYOH.INTERNAL.4"}, "NLI-BG13": {"type": "Host", "value": "***********", "name": "NLI-BG13"}, "NLHTESTMOBILE": {"type": "Host", "value": "***************", "name": "NLHTESTMOBILE"}, "NOVA.NLH.ORG.EXTERNAL": {"type": "Host", "value": "**************", "name": "NOVA.NLH.ORG.EXTERNAL"}, "BANDWIDTH_TEST_2": {"type": "Host", "value": "***************", "name": "BANDWIDTH_TEST_2"}, "NETWORK_OBJ_***************": {"type": "Host", "value": "***************", "name": "NETWORK_OBJ_***************"}, "NETWORK_OBJ_************": {"type": "Host", "value": "************", "name": "NETWORK_OBJ_************"}, "Barracuda.Web.NLH.Internal": {"type": "Host", "value": "***************", "name": "Barracuda.Web.NLH.Internal"}, "Barracuda.Email.NLH.Internal": {"type": "Host", "value": "***************", "name": "Barracuda.Email.NLH.Internal"}, "HEALTHTOUCH.EXTERNAL.PEER": {"type": "Host", "value": "*************", "name": "HEALTHTOUCH.EXTERNAL.PEER"}, "HEALTHTOUCH.PEER.INTERNAL.2": {"type": "Host", "value": "*************", "name": "HEALTHTOUCH.PEER.INTERNAL.2"}, "NETWORK_OBJ_*************": {"type": "Host", "value": "*************", "name": "NETWORK_OBJ_*************"}, "DMZ_TEST.NLH.ORG": {"type": "Host", "value": "**************", "name": "DMZ_TEST.NLH.ORG"}, "DUOTEST.NLH.ORG": {"type": "Host", "value": "**************", "name": "DUOTEST.NLH.ORG"}, "DUOTEST.NLH.ORG.DMZ": {"type": "Host", "value": "**************", "name": "DUOTEST.NLH.ORG.DMZ"}, "BARRACUDA.EMAIL.INSIDE": {"type": "Host", "value": "***************", "name": "BARRACUDA.EMAIL.INSIDE"}, "NLH.CORE.INTERNAL": {"type": "Host", "value": "**************", "name": "NLH.CORE.INTERNAL"}, "DCDS.CORE.INTERNAL": {"type": "Host", "value": "***************", "name": "DCDS.CORE.INTERNAL"}, "GPC_STACK": {"type": "Host", "value": "**************", "name": "GPC_STACK"}, "NLHSSI": {"type": "Host", "value": "**************", "name": "NLHSSI"}, "NLHBRAUNPUMPS.INTERNAL": {"type": "Host", "value": "***************", "name": "NLHBRAUNPUMPS.INTERNAL"}, "NLHBRAUNPUMPS.EXTERNAL": {"type": "Host", "value": "*************", "name": "NLHBRAUNPUMPS.EXTERNAL"}, "P-ITMGR": {"type": "Host", "value": "***************", "name": "P-ITMGR"}, "MEDIVATOR66838147": {"type": "Host", "value": "**************", "name": "MEDIVATOR66838147"}, "MEDIVATOR66838143": {"type": "Host", "value": "**************", "name": "MEDIVATOR66838143"}, "AMC.VPN.PEER.NEW": {"type": "Host", "value": "***************", "name": "AMC.VPN.PEER.NEW"}, "NUVODIA.INTERNAL.NEW.1": {"type": "Host", "value": "************", "name": "NUVODIA.INTERNAL.NEW.1"}, "NUVODIA.INTERNAL.NEW.2": {"type": "Host", "value": "************", "name": "NUVODIA.INTERNAL.NEW.2"}, "ULN.VPN.INTERNAL": {"type": "Host", "value": "************", "name": "ULN.VPN.INTERNAL"}, "CISCOPRIME.INTERNAL": {"type": "Host", "value": "***************", "name": "CISCOPRIME.INTERNAL"}, "CISCOPRIMEINF": {"type": "Host", "value": "***************", "name": "CISCOPRIMEINF"}, "MIS_TEST2": {"type": "Host", "value": "***************", "name": "MIS_TEST2"}, "CISCONMON": {"type": "Host", "value": "***************", "name": "CISCONMON"}, "SYSLOGSERVER": {"type": "Host", "value": "**************", "name": "SYSLOGSERVER"}, "NOVA-QIE.INTERNAL": {"type": "Host", "value": "**************", "name": "NOVA-QIE.INTERNAL"}, "NOVA.INTERLACE.PEER.EXTERNAL": {"type": "Host", "value": "*************", "name": "NOVA.INTERLACE.PEER.EXTERNAL"}, "WLC1": {"type": "Host", "value": "***************", "name": "WLC1"}, "sendgrid.net.virus": {"type": "Host", "value": "**************", "name": "sendgrid.net.virus"}, "NOVA.INTERLACE.PEER.EXTERNAL2": {"type": "Host", "value": "*************", "name": "NOVA.INTERLACE.PEER.EXTERNAL2"}, "love.explorethebest.com.spam.2": {"type": "Host", "value": "************", "name": "love.explorethebest.com.spam.2"}, "love.explorethebest.com.spam.1": {"type": "Host", "value": "************", "name": "love.explorethebest.com.spam.1"}, "love.explorethebest.com.spam.3": {"type": "Host", "value": "************", "name": "love.explorethebest.com.spam.3"}, "CISCO.WSA.INTERNAL": {"type": "Host", "value": "***************", "name": "CISCO.WSA.INTERNAL"}, "HARRIET.NLH.ORG": {"type": "Host", "value": "***************", "name": "HARRIET.NLH.ORG"}, "LUCIUS32.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS32.NLH.ORG"}, "LUCIUS10A.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS10A.NLH.ORG"}, "LUCIUS19B.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS19B.NLH.ORG"}, "WILLYWONKA.NLH.ORG": {"type": "Host", "value": "**************", "name": "WILLYWONKA.NLH.ORG"}, "NLHSYN01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSYN01.NLH.ORG"}, "NLHSYN02.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSYN02.NLH.ORG"}, "NLHSYN03.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSYN03.NLH.ORG"}, "NLHSYN04.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSYN04.NLH.ORG"}, "NLHSP19APP.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSP19APP.NLH.ORG"}, "LUCIUS18C.NLH.ORG": {"type": "Host", "value": "*************", "name": "LUCIUS18C.NLH.ORG"}, "LUCIUS19C.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS19C.NLH.ORG"}, "LUCIUS14.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS14.NLH.ORG"}, "ACRONIS.EXTERNAL.RANGE1": {"type": "Range", "value": "*************-*************27", "name": "ACRONIS.EXTERNAL.RANGE1"}, "ACRONIS.EXTERNAL.RANGE2": {"type": "Range", "value": "***********-*************", "name": "ACRONIS.EXTERNAL.RANGE2"}, "LUCIUS26D.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS26D.NLH.ORG"}, "DDPC.FIREALARM": {"type": "Host", "value": "**************", "name": "DDPC.FIREALARM"}, "LUCUIS16B.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCUIS16B.NLH.ORG"}, "LUCIUS17B.NLH.ORG": {"type": "Host", "value": "**************9", "name": "LUCIUS17B.NLH.ORG"}, "LUCIUS19A.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS19A.NLH.ORG"}, "SUMMIT.NLH.ORG": {"type": "Host", "value": "***************", "name": "SUMMIT.NLH.ORG"}, "LUCIUS25A.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS25A.NLH.ORG"}, "ONEVIEW.NLH.ORG": {"type": "Host", "value": "**************5", "name": "ONEVIEW.NLH.ORG"}, "DR1.NLH.ORG": {"type": "Host", "value": "**************3", "name": "DR1.NLH.ORG"}, "LUCIUS26B.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS26B.NLH.ORG"}, "NLHBACKUP02.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHBACKUP02.NLH.ORG"}, "KRONOSNEW.NLH.ORG": {"type": "Host", "value": "**************", "name": "KRONOSNEW.NLH.ORG"}, "SMHA.RAD.EXTERNAL.NEW": {"type": "Host", "value": "***************", "name": "SMHA.RAD.EXTERNAL.NEW"}, "BARRACUDA.CLOUD.EXTERNAL": {"type": "Range", "value": "************-**************", "name": "BARRACUDA.CLOUD.EXTERNAL"}, "ACRONIS.EXTERNAL.RANGE3": {"type": "Network", "value": "***********/24", "name": "ACRONIS.EXTERNAL.RANGE3"}, "ACRONIS.EXTERNAL.RANGE4": {"type": "Network", "value": "*************/24", "name": "ACRONIS.EXTERNAL.RANGE4"}, "BARRACUDA.LDAP.EXTERNAL.PEER.1": {"type": "Host", "value": "*************", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.1"}, "BARRACUDA.LDAP.EXTERNAL.PEER.2": {"type": "Host", "value": "*************", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.2"}, "BARRACUDA.LDAP.EXTERNAL.PEER.3": {"type": "Host", "value": "*************", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.3"}, "REYHEALTH.EXTERNAL.EXTERNAL.1": {"type": "Host", "value": "**************", "name": "REYHEALTH.EXTERNAL.EXTERNAL.1"}, "REYHEALTH.EXTERNAL.EXTERNAL.2": {"type": "Host", "value": "**************", "name": "REYHEALTH.EXTERNAL.EXTERNAL.2"}, "CHC.OPTUM.EXTERNAL.VPN.PEER": {"type": "Host", "value": "**************", "name": "CHC.OPTUM.EXTERNAL.VPN.PEER"}, "LUCIUS18D.NLH.ORG": {"type": "Host", "value": "*************", "name": "LUCIUS18D.NLH.ORG"}, "STREAMTASK.NLH.ORG": {"type": "Host", "value": "***************", "name": "STREAMTASK.NLH.ORG"}, "GPSUPPORT.VPN.EXTERNAL.PEER": {"type": "Host", "value": "*************", "name": "GPSUPPORT.VPN.EXTERNAL.PEER"}, "GESUPPORT.INTERNAL.NET": {"type": "Network", "value": "*********/16", "name": "GESUPPORT.INTERNAL.NET"}, "DI.AWSERVER": {"type": "Host", "value": "***************", "name": "DI.AWSERVER"}, "DI.AWSERVER.ILO": {"type": "Host", "value": "**************", "name": "DI.AWSERVER.ILO"}, "DI.CTSCANNER": {"type": "Host", "value": "**************", "name": "DI.CTSCANNER"}, "DI.CT.ADV.WS": {"type": "Host", "value": "**************", "name": "DI.CT.ADV.WS"}, "DI.GE.MAMMO.INTERFACE": {"type": "Host", "value": "**************", "name": "DI.GE.MAMMO.INTERFACE"}, "DI.MAMMO.SHUTTLE": {"type": "Host", "value": "**************", "name": "DI.MAMMO.SHUTTLE"}, "DI.MRI.ALLIANCE": {"type": "Host", "value": "**************", "name": "DI.MRI.ALLIANCE"}, "DI.MUSE01": {"type": "Host", "value": "**************", "name": "DI.MUSE01"}, "DI.MUSE02": {"type": "Host", "value": "**************", "name": "DI.MUSE02"}, "DI.MUSE03": {"type": "Host", "value": "*************7", "name": "DI.MUSE03"}, "DI.MAMMO": {"type": "Host", "value": "**************", "name": "DI.MAMMO"}, "DI.NUCMEDCAMERA": {"type": "Host", "value": "**************", "name": "DI.NUCMEDCAMERA"}, "DI.PETCTVIEWER": {"type": "Host", "value": "**************", "name": "DI.PETCTVIEWER"}, "DI.PERTH.XRAY": {"type": "Host", "value": "**************", "name": "DI.PERTH.XRAY"}, "DI.R.AND.F": {"type": "Host", "value": "**************", "name": "DI.R.AND.F"}, "DI.ROOMA": {"type": "Host", "value": "**************", "name": "DI.ROOMA"}, "DI.XELERIS.NM": {"type": "Host", "value": "**************", "name": "DI.XELERIS.NM"}, "NYOH.INTERNAL.5": {"type": "Host", "value": "************", "name": "NYOH.INTERNAL.5"}, "CLEARWATER1": {"type": "Host", "value": "**************", "name": "CLEARWATER1"}, "CLEARWATER2": {"type": "Host", "value": "*************", "name": "CLEARWATER2"}, "CLEARWATER3": {"type": "Range", "value": "**********-************", "name": "CLEARWATER3"}, "CLEARWATER4": {"type": "Range", "value": "************-**************", "name": "CLEARWATER4"}, "JELMENDORFSPAM": {"type": "Host", "value": "**************", "name": "JELMENDORFSPAM"}, "LUCIUS25C.NLH.ORG": {"type": "Host", "value": "*************5", "name": "LUCIUS25C.NLH.ORG"}, "PROVMDAPP.NLH.ORG": {"type": "Host", "value": "**************", "name": "PROVMDAPP.NLH.ORG"}, "NLHPROVMDORACLE.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHPROVMDORACLE.NLH.ORG"}, "NLHMUSE01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHMUSE01.NLH.ORG"}, "NLHMUSE02.NLH.ORG": {"type": "Host", "value": "*************7", "name": "NLHMUSE02.NLH.ORG"}, "LUCIUS16A.NLH.ORG": {"type": "Host", "value": "**************5", "name": "LUCIUS16A.NLH.ORG"}, "DESIGO.NLH.ORG": {"type": "Host", "value": "**************0", "name": "DESIGO.NLH.ORG"}, "backblazeb2.com": {"type": "FQDN", "value": "backblazeb2.com", "name": "backblazeb2.com"}, "HANYS.EXTERNAL.1": {"type": "FQDN", "value": "46453879m.hanys.org", "name": "HANYS.EXTERNAL.1"}, "HANYS.INTERNAL.2": {"type": "FQDN", "value": "mta0102-101.cd.hanys.org", "name": "HANYS.INTERNAL.2"}, "HANYS.EXTERNAL.3": {"type": "FQDN", "value": "mta0203-229.cd.hanys.org", "name": "HANYS.EXTERNAL.3"}, "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER": {"type": "Host", "value": "**************", "name": "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER"}, "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1": {"type": "Host", "value": "***********", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1"}, "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2": {"type": "Host", "value": "************", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2"}, "LUCIOUS01": {"type": "Host", "value": "**************", "name": "LUCIOUS01"}, "NLHPRTGPROBE04": {"type": "Host", "value": "**************", "name": "NLHPRTGPROBE04"}, "LUCIUS10C": {"type": "Host", "value": "***************", "name": "LUCIUS10C"}, "LUCIUS28": {"type": "Host", "value": "**************", "name": "LUCIUS28"}, "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3": {"type": "Host", "value": "*************", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3"}, "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4": {"type": "Host", "value": "**************", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4"}, "LUCIUS07.NLH.ORG": {"type": "Host", "value": "**************4", "name": "LUCIUS07.NLH.ORG"}, "NURSECALLAPP.NLH.ORG": {"type": "Host", "value": "**************", "name": "NURSECALLAPP.NLH.ORG"}, "NLHBRAUNPUMPS.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHBRAUNPUMPS.NLH.ORG"}, "BRAUNWEB": {"type": "Host", "value": "*************0", "name": "BRAUNWEB"}, "LUCIUS09A.NLH.ORG": {"type": "Host", "value": "*************04", "name": "LUCIUS09A.NLH.ORG"}, "LUCIUS09B.NLH.ORG": {"type": "Host", "value": "*************05", "name": "LUCIUS09B.NLH.ORG"}, "LUCIUS09C.NLH.ORG": {"type": "Host", "value": "*************06", "name": "LUCIUS09C.NLH.ORG"}, "NLHCISCO.NLH.ORG": {"type": "Host", "value": "*************7", "name": "NLHCISCO.NLH.ORG"}, "LUCIUS13.NLH.ORG": {"type": "Host", "value": "*************7", "name": "LUCIUS13.NLH.ORG"}, "SQLTEST.NLH.ORG": {"type": "Host", "value": "**************4", "name": "SQLTEST.NLH.ORG"}, "NLHMONITOR.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHMONITOR.NLH.ORG"}, "NLHPRTG01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHPRTG01.NLH.ORG"}, "NLHKIWISYSLOG01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHKIWISYSLOG01.NLH.ORG"}, "LUCIUS17A.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS17A.NLH.ORG"}, "XENAPP01.NLH.ORG": {"type": "Host", "value": "**************", "name": "XENAPP01.NLH.ORG"}, "CITRIXSF.NLH.ORG": {"type": "Host", "value": "*************53", "name": "CITRIXSF.NLH.ORG"}, "NLHWEB01..NLH.ORG": {"type": "Host", "value": "*************03", "name": "NLHWEB01..NLH.ORG"}, "AVAYACALLACCT.NLH.ORG": {"type": "Host", "value": "**************", "name": "AVAYACALLACCT.NLH.ORG"}, "NLHSSI.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHSSI.NLH.ORG"}, "TEMPTRAK.NLH.ORG": {"type": "Host", "value": "**************", "name": "TEMPTRAK.NLH.ORG"}, "PRINT.NLH.ORG": {"type": "Host", "value": "**************", "name": "PRINT.NLH.ORG"}, "QUICKCHARGE.NLH.ORG": {"type": "Host", "value": "**************", "name": "QUICKCHARGE.NLH.ORG"}, "NLH3M.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLH3M.NLH.ORG"}, "LUCIUS19D.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS19D.NLH.ORG"}, "NLHAV01.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHAV01.NLH.ORG"}, "LUCIUS23A.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS23A.NLH.ORG"}, "LUCIUS23B.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS23B.NLH.ORG"}, "LUCIUS23C.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS23C.NLH.ORG"}, "LUCIUS23D.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS23D.NLH.ORG"}, "NLHDHCP01.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHDHCP01.NLH.ORG"}, "LUCIUS25B.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS25B.NLH.ORG"}, "LUCIUS21.NLH.ORG": {"type": "Host", "value": "*************", "name": "LUCIUS21.NLH.ORG"}, "CENTRALINK.NLH.ORG": {"type": "Host", "value": "**************", "name": "CENTRALINK.NLH.ORG"}, "LUCIUS27.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS27.NLH.ORG"}, "HEALTHTOUCH02.NLH.ORG": {"type": "Host", "value": "**************", "name": "HEALTHTOUCH02.NLH.ORG"}, "MUSE03.NLH.ORG": {"type": "Host", "value": "**************", "name": "MUSE03.NLH.ORG"}, "KRONOSTEST.NLH.ORG": {"type": "Host", "value": "*************6", "name": "KRONOSTEST.NLH.ORG"}, "MUSE-TEST.NLH.ORG": {"type": "Host", "value": "**************6", "name": "MUSE-TEST.NLH.ORG"}, "INTERLACETEST.NLH.ORG": {"type": "Host", "value": "**************", "name": "INTERLACETEST.NLH.ORG"}, "NLHINT-TEST.NLH.ORG": {"type": "Host", "value": "**************2", "name": "NLHINT-TEST.NLH.ORG"}, "LUCIUS29.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS29.NLH.ORG"}, "NLHFUSION.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHFUSION.NLH.ORG"}, "MUSE-CCGHL7.NLH.ORG": {"type": "Host", "value": "**************", "name": "MUSE-CCGHL7.NLH.ORG"}, "LUCIUS31.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS31.NLH.ORG"}, "LUCIUS10B.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS10B.NLH.ORG"}, "LUCIUS10C.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS10C.NLH.ORG"}, "NLHCA.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHCA.NLH.ORG"}, "NLHPRTGPROBE3.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHPRTGPROBE3.NLH.ORG"}, "LUCIUS10D.NLH.ORG": {"type": "Host", "value": "***************", "name": "LUCIUS10D.NLH.ORG"}, "CODONICS.NLH.ORG": {"type": "Host", "value": "**************", "name": "CODONICS.NLH.ORG"}, "MDITEST.NLH.ORG": {"type": "Host", "value": "**************", "name": "MDITEST.NLH.ORG"}, "CITRIXFS02.NLH.ORG": {"type": "Host", "value": "***************", "name": "CITRIXFS02.NLH.ORG"}, "XENAPP02.NLH.ORG": {"type": "Host", "value": "**************7", "name": "XENAPP02.NLH.ORG"}, "MEDENTPRINT01.NLH.ORG": {"type": "Host", "value": "***************", "name": "MEDENTPRINT01.NLH.ORG"}, "HEALTHTOUCH01.NLH.ORG": {"type": "Host", "value": "**************", "name": "HEALTHTOUCH01.NLH.ORG"}, "LUCIUS18A.NLH.ORG": {"type": "Host", "value": "*************", "name": "LUCIUS18A.NLH.ORG"}, "INTERLACE.NLH.ORG": {"type": "Host", "value": "*************8", "name": "INTERLACE.NLH.ORG"}, "NOVA-QIE.NLH.ORG": {"type": "Host", "value": "**************", "name": "NOVA-QIE.NLH.ORG"}, "LUCIUS18B.NLH.ORG": {"type": "Host", "value": "*************", "name": "LUCIUS18B.NLH.ORG"}, "NLHUTILITY.NLH.ORG": {"type": "Host", "value": "*************7", "name": "NLHUTILITY.NLH.ORG"}, "NLHCODONICS.NLH.ORG": {"type": "Host", "value": "***************", "name": "NLHCODONICS.NLH.ORG"}, "NLHLICENSE.NLH.ORG": {"type": "Host", "value": "*************1", "name": "NLHLICENSE.NLH.ORG"}, "HPDMAN.NLH.ORG": {"type": "Host", "value": "***************", "name": "HPDMAN.NLH.ORG"}, "SCVMM.NLH.ORG": {"type": "Host", "value": "**************2", "name": "SCVMM.NLH.ORG"}, "LUCIUS24A.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS24A.NLH.ORG"}, "LUCIUS24B.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS24B.NLH.ORG"}, "LUCIUS24C.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS24C.NLH.ORG"}, "LUCIUS24D.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS24D.NLH.ORG"}, "LUCIUS26A.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS26A.NLH.ORG"}, "ESICALLACCT26A.NLH.ORG": {"type": "Host", "value": "**************", "name": "ESICALLACCT26A.NLH.ORG"}, "ESRS.NLH.ORG": {"type": "Host", "value": "**************", "name": "ESRS.NLH.ORG"}, "NLHDRFIRST.NLH.ORG": {"type": "Host", "value": "**************7", "name": "NLHDRFIRST.NLH.ORG"}, "NLHELOCK.NLH.ORG": {"type": "Host", "value": "*************1", "name": "NLHELOCK.NLH.ORG"}, "LUCIUS26C.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS26C.NLH.ORG"}, "COBAS.NLH.ORG": {"type": "Host", "value": "*************6", "name": "COBAS.NLH.ORG"}, "PRADEV.NLH.ORG": {"type": "Host", "value": "***************", "name": "PRADEV.NLH.ORG"}, "NLHADMINCENTER.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLHADMINCENTER.NLH.ORG"}, "LUCIUS28.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS28.NLH.ORG"}, "NURSECALLHD.NLH.ORG": {"type": "Host", "value": "**************", "name": "NURSECALLHD.NLH.ORG"}, "NLH-iUV.NLH.ORG": {"type": "Host", "value": "**************", "name": "NLH-iUV.NLH.ORG"}, "LUCIUS30.NLH.ORG": {"type": "Host", "value": "**************", "name": "LUCIUS30.NLH.ORG"}, "MUSE-APP.NLH.ORG": {"type": "Host", "value": "**************0", "name": "MUSE-APP.NLH.ORG"}, "MUSE-NXWEB.NLH.ORG": {"type": "Host", "value": "**************", "name": "MUSE-NXWEB.NLH.ORG"}, "Clearwater.External.Peer": {"type": "Host", "value": "************", "name": "Clearwater.External.Peer"}, "Clearwater.Internal.Peer.Range": {"type": "Network", "value": "*************/28", "name": "Clearwater.Internal.Peer.Range"}, "ASA01": {"type": "Host", "value": "*************", "name": "ASA01"}, "ASA02": {"type": "Host", "value": "*************", "name": "ASA02"}, "NLH.Firewall.Range.Internal": {"type": "Network", "value": "*************/30", "name": "NLH.Firewall.Range.Internal"}, "NETWORK_OBJ_*************": {"type": "Host", "value": "*************", "name": "NETWORK_OBJ_*************"}, "NETWORK_OBJ_*************": {"type": "Host", "value": "*************", "name": "NETWORK_OBJ_*************"}, "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1": {"type": "Host", "value": "**********", "name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1"}, "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2": {"type": "Host", "value": "***********", "name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2"}, "SSI.EXTERNAL.PEER.1": {"type": "Network", "value": "*********/24", "name": "SSI.EXTERNAL.PEER.1"}, "MICROSOFTSTREAM.COM": {"type": "FQDN", "value": "MICROSOFTSTREAM.COM", "name": "MICROSOFTSTREAM.COM"}, "MMI.BILLING.EXTERNAL.PEER": {"type": "Host", "value": "************", "name": "MMI.BILLING.EXTERNAL.PEER"}, "MMI.BILLING.INTERNAL.PEER": {"type": "Host", "value": "***********", "name": "MMI.BILLING.INTERNAL.PEER"}, "NYOH.INTERNAL.MEDICOM": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.MEDICOM"}, "NYOH.INTERNAL.AMBRA": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.AMBRA"}, "NYOH.INTERNAL.POWERSHARE": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.POWERSHARE"}, "NYOH.INTERNAL.CLOUD": {"type": "Host", "value": "***********", "name": "NYOH.INTERNAL.CLOUD"}, "Nuvodia.OneOncology.Cloud.External.Peer": {"type": "Host", "value": "************", "name": "Nuvodia.OneOncology.Cloud.External.Peer"}, "Nuvodia.OneOncology.Cloud.Internal.Peer": {"type": "Host", "value": "**************", "name": "Nuvodia.OneOncology.Cloud.Internal.Peer"}, "NETWORK_OBJ_*************": {"type": "Host", "value": "*************", "name": "NETWORK_OBJ_*************"}, "NUVODIA.INTERNAL.NEW.3": {"type": "Host", "value": "*************", "name": "NUVODIA.INTERNAL.NEW.3"}, "FRESHWORKS.EXCLUSIONS.1": {"type": "Host", "value": "*************", "name": "FRESHWORKS.EXCLUSIONS.1"}, "FRESHWORKS.EXCLUSIONS.2": {"type": "Host", "value": "***********", "name": "FRESHWORKS.EXCLUSIONS.2"}, "FRESHWORKS.EXCLUSIONS.3": {"type": "Host", "value": "************", "name": "FRESHWORKS.EXCLUSIONS.3"}, "FRESHWORKS.EXCLUSIONS.5": {"type": "Host", "value": "*************", "name": "FRESHWORKS.EXCLUSIONS.5"}, "FRESHWORKS.EXCLUSIONS.6": {"type": "Host", "value": "*************", "name": "FRESHWORKS.EXCLUSIONS.6"}, "FRESHWORKS.EXCLUSIONS.7": {"type": "Host", "value": "*************", "name": "FRESHWORKS.EXCLUSIONS.7"}}, "object_groups": {"PaceGlobalgrp": {"type": "service", "name": "PaceGlobalgrp", "members": [{"type": "port", "value": "6544"}, {"type": "port", "value": "2222"}, {"type": "port", "value": "2020"}, {"type": "port", "value": "www"}, {"type": "port", "value": "telnet"}, {"type": "port", "value": "53374"}, {"type": "port", "value": "53372"}, {"type": "port", "value": "53050"}, {"type": "port", "value": "53048"}]}, "timeservice": {"type": "service", "name": "timeservice", "members": [{"type": "range", "value": "123-123"}, {"type": "port", "value": "daytime"}]}, "timeserviceUDP": {"type": "service", "name": "timeserviceUDP", "members": [{"type": "port", "value": "13"}, {"type": "port", "value": "time"}, {"type": "port", "value": "ntp"}]}, "Medivators": {"type": "network", "name": "Medivators", "members": [{"type": "object", "name": "MEDIVATOR66838143"}, {"type": "object", "name": "MEDIVATOR66838147"}]}, "QUEST": {"type": "service", "name": "QUEST", "members": [{"type": "range", "value": "57010-57013"}]}, "citrixXML": {"type": "service", "name": "citrixXML", "members": [{"type": "port", "value": "citrix-ica"}, {"type": "port", "value": "www"}, {"type": "port", "value": "8080"}, {"type": "port", "value": "https"}]}, "GatewayDMZ": {"type": "service", "name": "GatewayDMZ", "members": [{"type": "port", "value": "citrix-ica"}, {"type": "port", "value": "ftp"}, {"type": "port", "value": "3389"}, {"type": "port", "value": "8080"}, {"type": "port", "value": "www"}, {"type": "port", "value": "2598"}, {"type": "port", "value": "https"}, {"type": "port", "value": "ldap"}, {"type": "port", "value": "8008"}]}, "RSA": {"type": "service", "name": "RSA", "members": [{"type": "range", "value": "5505-5570"}]}, "HFMBoces": {"type": "service", "name": "HFMBoces", "members": [{"type": "port", "value": "pop3"}, {"type": "port", "value": "www"}, {"type": "port", "value": "https"}, {"type": "port", "value": "smtp"}]}, "NUVODIA.INTERNAL.GROUP.NEW": {"type": "network", "name": "NUVODIA.INTERNAL.GROUP.NEW", "members": [{"type": "object", "name": "NUVODIA.INTERNAL.NEW.1"}, {"type": "object", "name": "NUVODIA.INTERNAL.NEW.2"}]}, "NUVODIA.INTERNAL.PEER.NET1": {"type": "network", "name": "NUVODIA.INTERNAL.PEER.NET1", "members": [{"type": "object", "name": "NUVODIA.INTERNAL.1"}, {"type": "object", "name": "NUVODIA.INTERNAL.2"}]}, "DM_INLINE_NETWORK_4": {"type": "network", "name": "DM_INLINE_NETWORK_4", "members": [{"type": "group", "name": "NUVODIA.INTERNAL.GROUP.NEW"}, {"type": "group", "name": "NUVODIA.INTERNAL.PEER.NET1"}]}, "DM_INLINE_NETWORK_6": {"type": "network", "name": "DM_INLINE_NETWORK_6", "members": [{"type": "object", "name": "STRAT_SOL.NET.INTERNAL1"}, {"type": "object", "name": "STRAT_SOL.NET.INTERNAL2"}]}, "FoodService": {"type": "network", "name": "FoodService", "members": [{"type": "object", "name": "P_FS_SEC"}]}, "GEinbound": {"type": "service", "name": "GEinbound", "members": [{"type": "port", "value": "3128"}, {"type": "port", "value": "8100"}, {"type": "port", "value": "4444"}, {"type": "port", "value": "ftp-data"}, {"type": "port", "value": "ftp"}, {"type": "port", "value": "5800"}, {"type": "port", "value": "exec"}, {"type": "port", "value": "www"}, {"type": "range", "value": "2327-2328"}, {"type": "range", "value": "3276-3277"}, {"type": "port", "value": "8080"}, {"type": "port", "value": "5900"}, {"type": "port", "value": "telnet"}, {"type": "port", "value": "3003"}]}, "GEoutbound": {"type": "service", "name": "GEoutbound", "members": [{"type": "port", "value": "ftp"}, {"type": "range", "value": "6000-6200"}, {"type": "port", "value": "8002"}, {"type": "port", "value": "ftp-data"}, {"type": "port", "value": "www"}, {"type": "port", "value": "7979"}]}, "DI.Net.Group": {"type": "network", "name": "DI.Net.Group", "members": [{"type": "object", "name": "CTScanner"}, {"type": "object", "name": "<PERSON><PERSON><PERSON>"}, {"type": "object", "name": "RandF"}]}, "PetLinks": {"type": "service", "name": "PetLinks", "members": [{"type": "range", "value": "21000-21004"}, {"type": "range", "value": "16000-16004"}, {"type": "port", "value": "8899"}]}, "STRATEGICSOLUTIONS.EXTERNAL.GROUP": {"type": "network", "name": "STRATEGICSOLUTIONS.EXTERNAL.GROUP", "members": [{"type": "object", "name": "STRAT_SOL.NET.INTERNAL1"}, {"type": "object", "name": "STRAT_SOL.NET.INTERNAL2"}]}, "TeleVideoTcpUdp": {"type": "service", "name": "TeleVideoTcpUdp", "members": [{"type": "range", "value": "3230-3253"}, {"type": "port", "value": "1503"}, {"type": "port", "value": "1731"}, {"type": "port", "value": "1300"}, {"type": "range", "value": "1718-1720"}]}, "GEPACS": {"type": "service", "name": "GEPACS", "members": [{"type": "port", "value": "522"}, {"type": "port", "value": "ldap"}, {"type": "port", "value": "www"}, {"type": "port", "value": "3120"}, {"type": "port", "value": "20000"}, {"type": "port", "value": "3320"}, {"type": "range", "value": "953-999"}, {"type": "port", "value": "3389"}]}, "ExchangePorts": {"type": "service", "name": "ExchangePorts", "members": [{"type": "port", "value": "https"}, {"type": "port", "value": "20443"}, {"type": "port", "value": "137"}, {"type": "port", "value": "161"}, {"type": "port", "value": "9100"}]}, "PrintPorts": {"type": "service", "name": "PrintPorts", "members": [{"type": "port", "value": "lpd"}, {"type": "port", "value": "9100"}]}, "PrinterPorts": {"type": "service", "name": "PrinterPorts", "members": [{"type": "port", "value": "lpd"}, {"type": "port", "value": "9100"}]}, "Cardinal": {"type": "network", "name": "<PERSON>", "members": [{"type": "object", "name": "Cardinal132"}, {"type": "object", "name": "Cardinal133"}, {"type": "object", "name": "Cardinal144"}, {"type": "object", "name": "Cardinal145"}, {"type": "object", "name": "Cardinal176"}, {"type": "object", "name": "Cardinal177"}, {"type": "object", "name": "Cardinal194"}, {"type": "object", "name": "Cardinal195"}]}, "medinotes": {"type": "network", "name": "medinotes", "members": [{"type": "object", "name": "<PERSON><PERSON><PERSON>"}, {"type": "object", "name": "medinote2"}, {"type": "object", "name": "medinote1"}]}, "IPSEC_ISAKMP": {"type": "service", "name": "IPSEC_ISAKMP", "members": [{"type": "port", "value": "isakmp"}, {"type": "port", "value": "4500"}]}, "EmdeonPorts": {"type": "service", "name": "EmdeonPorts", "members": [{"type": "port", "value": "5002"}, {"type": "port", "value": "https"}]}, "in_any_to_out_any_tcp": {"type": "service", "name": "in_any_to_out_any_tcp", "members": [{"type": "port", "value": "8080"}, {"type": "port", "value": "citrix-ica"}, {"type": "port", "value": "1755"}, {"type": "port", "value": "ssh"}, {"type": "range", "value": "6101-6102"}, {"type": "port", "value": "3389"}, {"type": "port", "value": "www"}, {"type": "port", "value": "https"}, {"type": "port", "value": "ftp"}, {"type": "port", "value": "ftp-data"}, {"type": "port", "value": "9080"}, {"type": "port", "value": "netbios-ssn"}, {"type": "port", "value": "138"}, {"type": "port", "value": "137"}, {"type": "range", "value": "55443-55443"}, {"type": "range", "value": "25859-25859"}, {"type": "range", "value": "8383-8383"}, {"type": "port", "value": "telnet"}, {"type": "range", "value": "9999-9999"}, {"type": "range", "value": "27014-27050"}, {"type": "range", "value": "4080-4080"}, {"type": "range", "value": "6881-6999"}, {"type": "port", "value": "1119"}, {"type": "port", "value": "5101"}, {"type": "port", "value": "3653"}, {"type": "port", "value": "2591"}, {"type": "range", "value": "1852-1855"}, {"type": "range", "value": "48000-49000"}, {"type": "range", "value": "1688-1688"}, {"type": "range", "value": "8150-8150"}, {"type": "range", "value": "11001-11001"}, {"type": "range", "value": "1024-1029"}, {"type": "port", "value": "2048"}, {"type": "range", "value": "sip-5061"}, {"type": "range", "value": "30000-31000"}, {"type": "port", "value": "25565"}, {"type": "port", "value": "57773"}, {"type": "port", "value": "5938"}, {"type": "port", "value": "5228"}, {"type": "range", "value": "8000-8000"}, {"type": "port", "value": "990"}, {"type": "port", "value": "1935"}, {"type": "port", "value": "5500"}, {"type": "port", "value": "50025"}, {"type": "range", "value": "65007-65008"}, {"type": "port", "value": "6101"}, {"type": "port", "value": "900"}, {"type": "port", "value": "4080"}, {"type": "port", "value": "9443"}, {"type": "port", "value": "65009"}, {"type": "port", "value": "8443"}, {"type": "port", "value": "2443"}, {"type": "port", "value": "5494"}, {"type": "port", "value": "49210"}, {"type": "port", "value": "47290"}]}, "CitrixServers.dmz": {"type": "network", "name": "CitrixServers.dmz", "members": [{"type": "object", "name": "NATHAN3.dmz"}, {"type": "object", "name": "NATHAN5.dmz"}, {"type": "object", "name": "NATHAN1.dmz"}, {"type": "object", "name": "NATHAN4.dmz"}, {"type": "object", "name": "NATHAN9.dmz"}, {"type": "object", "name": "NATHAN10.dmz"}, {"type": "object", "name": "NATHAN11.dmz"}]}, "RAMSOFTports": {"type": "service", "name": "RAMSOFTports", "members": [{"type": "range", "value": "12800-12820"}]}, "MilleniumPACS": {"type": "network", "name": "MilleniumPACS", "members": [{"type": "object", "name": "MilleniumPACS2"}, {"type": "object", "name": "MilleniumPACS1"}, {"type": "object", "name": "MilleniumPACS3"}, {"type": "object", "name": "MilleniumPACS4"}, {"type": "object", "name": "MilleniumPACS5"}]}, "CoreFTP": {"type": "service", "name": "CoreFTP", "members": [{"type": "port", "value": "990"}, {"type": "range", "value": "1900-1930"}]}, "PhilipsPacs": {"type": "service", "name": "PhilipsPacs", "members": [{"type": "port", "value": "104"}, {"type": "port", "value": "ldap"}, {"type": "port", "value": "https"}, {"type": "port", "value": "7575"}, {"type": "port", "value": "8192"}, {"type": "port", "value": "2068"}, {"type": "port", "value": "telnet"}, {"type": "port", "value": "3211"}, {"type": "port", "value": "ssh"}, {"type": "port", "value": "6464"}, {"type": "port", "value": "1155"}, {"type": "port", "value": "www"}]}, "ExchangeServers": {"type": "network", "name": "ExchangeServers", "members": [{"type": "object", "name": "NLHEXCHANGE.NLH.ORG"}]}, "TeleMedVT": {"type": "network", "name": "TeleMedVT", "members": [{"type": "object", "name": "TeleMedVT3"}, {"type": "object", "name": "TelemedVT4"}, {"type": "object", "name": "TelemedVT5"}, {"type": "object", "name": "TeleMedVT1"}]}, "PacsServers": {"type": "network", "name": "PacsServers", "members": [{"type": "object", "name": "PACS"}, {"type": "object", "name": "PACS_CACHE"}, {"type": "object", "name": "PACS_STORE1"}, {"type": "object", "name": "PACS_STORE2"}, {"type": "object", "name": "PACS_STORE144"}]}, "Pacs": {"type": "service", "name": "Pacs", "members": [{"type": "port", "value": "6464"}, {"type": "port", "value": "https"}, {"type": "port", "value": "7575"}]}, "NexTalk1": {"type": "service", "name": "NexTalk1", "members": [{"type": "port", "value": "2591"}]}, "NexTalkTcpUdp": {"type": "service", "name": "NexTalkTcpUdp", "members": [{"type": "port", "value": "1853"}]}, "CastleSys": {"type": "service", "name": "CastleSys", "members": [{"type": "port", "value": "9011"}, {"type": "port", "value": "9006"}]}, "MDI.OUT.Allow": {"type": "network", "name": "MDI.OUT.<PERSON>ow", "members": [{"type": "object", "name": "Medent.VPN.net"}, {"type": "object", "name": "Spantel.Prod"}, {"type": "object", "name": "SpantelHL7.test"}]}, "eRXdataCenters": {"type": "network", "name": "eRXdataCenters", "members": [{"type": "object", "name": "eRXcenter2"}, {"type": "object", "name": "eRXcenter3"}, {"type": "object", "name": "eRXcenter1"}, {"type": "object", "name": "eRxChicago"}, {"type": "object", "name": "eRxDallas"}]}, "Medent.Interface": {"type": "network", "name": "Medent.Interface", "members": [{"type": "object", "name": "MedentRemote"}, {"type": "object", "name": "Medent.RPTS"}]}, "FTPpsv5500": {"type": "service", "name": "FTPpsv5500", "members": [{"type": "range", "value": "5500-5700"}, {"type": "port", "value": "ftp"}]}, "Labcorp": {"type": "service", "name": "Labcorp", "members": [{"type": "port", "value": "3611"}, {"type": "port", "value": "30032"}]}, "Labcorptcp": {"type": "service", "name": "Labcorptcp", "members": [{"type": "port", "value": "ftp"}, {"type": "port", "value": "ftp-data"}, {"type": "port", "value": "3611"}, {"type": "port", "value": "30032"}]}, "SMHA.RAD": {"type": "network", "name": "SMHA.RAD", "members": [{"type": "object", "name": "SMHA.ps1"}, {"type": "object", "name": "SMHA.ps2"}, {"type": "object", "name": "SMHA.ps3"}, {"type": "object", "name": "SMHA.ps4"}, {"type": "object", "name": "SMHA.syn1"}, {"type": "object", "name": "SMHA.syn2"}, {"type": "object", "name": "SMHA.orpc1"}, {"type": "object", "name": "SMHA.orpc2"}, {"type": "object", "name": "SMHA.orpc3"}, {"type": "object", "name": "SMHA.read1"}, {"type": "object", "name": "SMHA.read2"}, {"type": "object", "name": "SMHA.read3"}, {"type": "object", "name": "SMHA.KPServer"}, {"type": "object", "name": "SMHA.read4"}, {"type": "object", "name": "smha.mammo"}, {"type": "object", "name": "smha.pacsed30"}, {"type": "object", "name": "smha.pacrd06"}, {"type": "object", "name": "SMHApacsSUBNET"}, {"type": "network", "value": "10.57.50.189/32"}, {"type": "network", "value": "10.57.4.67/32"}, {"type": "network", "value": "10.57.24.205/32"}, {"type": "network", "value": "10.57.24.206/32"}, {"type": "network", "value": "10.57.5.89/32"}, {"type": "network", "value": "10.57.4.201/32"}, {"type": "object", "name": "SMHA.read5"}, {"type": "object", "name": "SMHA.read6"}, {"type": "object", "name": "SHMA.read7"}, {"type": "object", "name": "SMHA.read8"}, {"type": "object", "name": "SMHA.read9"}, {"type": "object", "name": "SMHA.read10"}, {"type": "network", "value": "*************/32"}, {"type": "network", "value": "*************/32"}, {"type": "network", "value": "************/32"}, {"type": "network", "value": "************/32"}, {"type": "object", "name": "SMHA.Synapse.Dest"}, {"type": "object", "name": "SMHA.pacs1"}, {"type": "object", "name": "SMHA.pacs2"}, {"type": "object", "name": "SMHA.pacs3"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**********"}, {"type": "object", "name": "SMHA.READ.10"}]}, "RAD.PACS.READ": {"type": "network", "name": "RAD.PACS.READ", "members": [{"type": "object", "name": "P-DI-MGR"}, {"type": "object", "name": "PACS_READ3_NEW"}, {"type": "object", "name": "P_CIO1"}, {"type": "object", "name": "P_DI_NUMED"}, {"type": "object", "name": "US_LOGI_E9"}, {"type": "object", "name": "MAMMO40"}, {"type": "object", "name": "MOMMO41"}, {"type": "object", "name": "PACS"}, {"type": "object", "name": "PACS_CACHE"}, {"type": "object", "name": "PACS_STORE1"}, {"type": "object", "name": "PACS_STORE2"}, {"type": "object", "name": "phil<PERSON><PERSON><PERSON>"}, {"type": "object", "name": "pacs.net"}, {"type": "object", "name": "PACS_VCE"}, {"type": "object", "name": "INTERLACE"}]}, "IVANStcp": {"type": "service", "name": "IVANStcp", "members": [{"type": "port", "value": "5053"}, {"type": "port", "value": "daytime"}, {"type": "port", "value": "9920"}, {"type": "port", "value": "ldap"}, {"type": "port", "value": "709"}, {"type": "port", "value": "ftp"}, {"type": "port", "value": "5080"}, {"type": "port", "value": "3101"}]}, "IVANSudp": {"type": "service", "name": "IVANSudp", "members": [{"type": "port", "value": "isakmp"}, {"type": "port", "value": "5081"}, {"type": "port", "value": "domain"}]}, "SOPHOS": {"type": "network", "name": "SOPHOS", "members": [{"type": "object", "name": "SOPHOSWEB"}, {"type": "object", "name": "SOPHOSEMAIL"}]}, "Sophos": {"type": "service", "name": "<PERSON>ph<PERSON>", "members": [{"type": "port", "value": "10443"}, {"type": "port", "value": "ssh"}, {"type": "port", "value": "444"}, {"type": "port", "value": "smtp"}, {"type": "port", "value": "www"}, {"type": "port", "value": "https"}]}, "any_in_udp_to_any_out": {"type": "service", "name": "any_in_udp_to_any_out", "members": [{"type": "port", "value": "nameserver"}, {"type": "port", "value": "www"}, {"type": "port", "value": "domain"}, {"type": "port", "value": "time"}, {"type": "port", "value": "ntp"}, {"type": "port", "value": "netbios-ns"}, {"type": "port", "value": "139"}, {"type": "port", "value": "netbios-dgm"}, {"type": "range", "value": "27000-27015"}, {"type": "range", "value": "6881-6999"}, {"type": "port", "value": "1119"}, {"type": "port", "value": "isakmp"}, {"type": "port", "value": "201"}, {"type": "port", "value": "4500"}, {"type": "range", "value": "sip-5061"}, {"type": "port", "value": "1854"}, {"type": "port", "value": "1855"}, {"type": "port", "value": "1853"}, {"type": "port", "value": "1852"}, {"type": "range", "value": "30000-31000"}, {"type": "port", "value": "34788"}]}, "CitrixServers1": {"type": "network", "name": "CitrixServers1", "members": [{"type": "object", "name": "XENAPP02"}, {"type": "object", "name": "XENAPP30"}]}, "CitrixServers1_ref": {"type": "network", "name": "CitrixServers1_ref", "members": [{"type": "object", "name": "NATHAN3.dmz"}, {"type": "object", "name": "NATHAN4.dmz"}, {"type": "object", "name": "NATHAN5.dmz"}, {"type": "object", "name": "NATHAN6.dmz"}, {"type": "object", "name": "NATHAN1.dmz"}]}, "SophosMail": {"type": "service", "name": "SophosMail", "members": [{"type": "port", "value": "10443"}, {"type": "port", "value": "smtp"}, {"type": "port", "value": "https"}]}, "BobSFTP": {"type": "service", "name": "BobSFTP", "members": [{"type": "range", "value": "21000-21100"}]}, "MVO_Allow_OUTBOUND_Group": {"type": "network", "name": "MVO_Allow_OUTBOUND_Group", "members": [{"type": "object", "name": "pacs.net_1"}, {"type": "object", "name": "ORTIZ_LT"}, {"type": "object", "name": "p_mis_netadmin"}, {"type": "object", "name": "PACS_OR3"}, {"type": "object", "name": "PACS_OR1"}, {"type": "object", "name": "PACS_OR2"}, {"type": "object", "name": "PACS_DI"}, {"type": "object", "name": "DR_CECIL"}]}, "ProvationServers": {"type": "network", "name": "ProvationServers", "members": [{"type": "object", "name": "NLHDC1_IPMI"}, {"type": "object", "name": "NLHDC2_IPMI"}]}, "AAI.NYOH.PACS": {"type": "network", "name": "AAI.NYOH.PACS", "members": [{"type": "object", "name": "AAI.120"}, {"type": "object", "name": "AAI.124"}, {"type": "object", "name": "AAI.125"}, {"type": "object", "name": "AAI.52"}]}, "Impulse.UDP": {"type": "service", "name": "Impulse.UDP", "members": [{"type": "port", "value": "isakmp"}, {"type": "port", "value": "4500"}]}, "ImpulseTCP": {"type": "service", "name": "ImpulseTCP", "members": [{"type": "port", "value": "51"}, {"type": "port", "value": "50"}]}, "TEMP_TRACK1": {"type": "service", "name": "TEMP_TRACK1", "members": [{"type": "range", "value": "1001-1001"}]}, "Dolby_OUT": {"type": "network", "name": "Dolby_OUT", "members": [{"type": "network", "value": "*********/24"}]}, "Dolby_Servers": {"type": "network", "name": "Dolby_Servers", "members": [{"type": "object", "name": "INTERLACE"}, {"type": "object", "name": "DICTATION02"}, {"type": "object", "name": "DOLBEY"}, {"type": "object", "name": "DOLBEYTEST"}]}, "Healthtouch.out": {"type": "network", "name": "Healthtouch.out", "members": [{"type": "network", "value": "***********/32"}]}, "FoodSVC": {"type": "network", "name": "FoodSVC", "members": [{"type": "object", "name": "HEALTHTOUCH01"}, {"type": "object", "name": "HEALTHTOUCH02"}]}, "PatPortal": {"type": "service", "name": "<PERSON><PERSON><PERSON><PERSON>", "members": [{"type": "range", "value": "55443-55443"}]}, "ALLSCRIPT_PORTAL": {"type": "service", "name": "ALLSCRIPT_PORTAL", "members": [{"type": "range", "value": "55443-55443"}]}, "testgroup": {"type": "service", "name": "testgroup", "members": [{"type": "range", "value": "8383-8383"}]}, "ALBANYMEDPACS": {"type": "service", "name": "ALBANYMEDPACS", "members": [{"type": "range", "value": "exec-exec"}, {"type": "range", "value": "104-104"}]}, "ALBANYPACS.GROUP": {"type": "network", "name": "ALBANYPACS.GROUP", "members": [{"type": "object", "name": "ALBANYMED.IN.2"}, {"type": "object", "name": "ALBANYMED.IN"}]}, "HIXNY": {"type": "network", "name": "HIXNY", "members": [{"type": "object", "name": "hixny.com_integration"}, {"type": "object", "name": "hixny.com_prod"}, {"type": "object", "name": "webservices.hixny.com"}]}, "Guest_Wireless": {"type": "service", "name": "Guest_Wireless", "members": [{"type": "port", "value": "www"}, {"type": "port", "value": "https"}]}, "SOPHOSFTP": {"type": "service", "name": "SOPHOSFTP", "members": [{"type": "range", "value": "5000-51000"}]}, "Olympus.inside.group": {"type": "network", "name": "Olympus.inside.group", "members": [{"type": "object", "name": "Olympus.Inside.New"}]}, "BOCES_IPADS": {"type": "service", "name": "BOCES_IPADS", "members": [{"type": "port", "value": "1640"}, {"type": "port", "value": "www"}, {"type": "port", "value": "5223"}, {"type": "range", "value": "2195-2196"}, {"type": "port", "value": "https"}]}, "MDI_Group": {"type": "network", "name": "MDI_Group", "members": [{"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "MDITEST"}]}, "Brian_DHCP": {"type": "network", "name": "Brian_DHCP", "members": [{"type": "object", "name": "BRIAN_DHCP"}]}, "Schumacher.Inside": {"type": "network", "name": "Schumacher.Inside", "members": [{"type": "network", "value": "*************/32"}, {"type": "network", "value": "*************/32"}, {"type": "object", "name": "Schumacher.Inside1.new.ADTPROD"}, {"type": "object", "name": "Schumacher.Inside2.new.ADTTEST"}]}, "TEST": {"type": "service", "name": "TEST", "members": [{"type": "range", "value": "9960-9969"}, {"type": "port", "value": "29900"}, {"type": "port", "value": "18120"}, {"type": "port", "value": "18000"}, {"type": "range", "value": "1024-1124"}, {"type": "port", "value": "18060"}, {"type": "port", "value": "28910"}, {"type": "port", "value": "27900"}]}, "MEDENTHQ": {"type": "network", "name": "MEDENTHQ", "members": [{"type": "network", "value": "71.176.110.5/32"}, {"type": "network", "value": "96.238.145.164/32"}, {"type": "network", "value": "***********/32"}, {"type": "network", "value": "108.12.29.3/32"}, {"type": "network", "value": "65.114.41.7/32"}]}, "MEDENT_GROUP": {"type": "network", "name": "MEDENT_GROUP", "members": [{"type": "object", "name": "MEDENT"}, {"type": "object", "name": "MEDENT03"}, {"type": "object", "name": "MEDENT05"}, {"type": "object", "name": "MEDENT_NAS_INTERNAL"}, {"type": "object", "name": "MEDENT-EXPORT"}]}, "IMO_Ports": {"type": "service", "name": "IMO_Ports", "members": [{"type": "port", "value": "42053"}, {"type": "port", "value": "42027"}, {"type": "port", "value": "42051"}, {"type": "port", "value": "42011"}, {"type": "port", "value": "42045"}]}, "TeamViewer": {"type": "service", "name": "TeamViewer", "members": [{"type": "port", "value": "5938"}]}, "CCD_MESSAGING": {"type": "service", "name": "CCD_MESSAGING", "members": [{"type": "port", "value": "53374"}, {"type": "port", "value": "53050"}, {"type": "port", "value": "53372"}, {"type": "port", "value": "53048"}, {"type": "port", "value": "www"}]}, "WINDOWS_XP_DENY": {"type": "network", "name": "WINDOWS_XP_DENY", "members": [{"type": "object", "name": "easyeeg"}, {"type": "object", "name": "VENUE50_p_pacs_cdburn"}, {"type": "object", "name": "INFINIA"}]}, "APPLE.OUT": {"type": "network", "name": "APPLE.OUT", "members": [{"type": "network", "value": "17.173.254.223/32"}, {"type": "network", "value": "17.173.254.222/32"}, {"type": "network", "value": "17.172.233.0/24"}, {"type": "network", "value": "17.173.255.0/24"}, {"type": "network", "value": "17.172.232.0/24"}, {"type": "network", "value": "17.154.239.222/32"}, {"type": "network", "value": "17.154.239.223/32"}, {"type": "network", "value": "17.155.127.223/32"}, {"type": "network", "value": "17.155.127.222/32"}]}, "Apple_Services": {"type": "service", "name": "Apple_Services", "members": [{"type": "range", "value": "16380-16390"}]}, "ProviderOrg.External": {"type": "network", "name": "ProviderOrg.External", "members": [{"type": "network", "value": "70.42.47.133/32"}, {"type": "network", "value": "70.42.64.1/32"}, {"type": "object", "name": "hixny.com_integration"}, {"type": "object", "name": "hixny.com_prod"}, {"type": "object", "name": "integration.hixny.com"}, {"type": "object", "name": "webservices.hixny.com"}, {"type": "network", "value": "199.244.76.168/32"}, {"type": "network", "value": "199.244.76.206/32"}, {"type": "network", "value": "199.244.76.207/32"}, {"type": "network", "value": "199.244.76.208/32"}, {"type": "network", "value": "199.244.76.209/32"}, {"type": "network", "value": "199.244.76.201/32"}, {"type": "network", "value": "199.244.76.202/32"}, {"type": "network", "value": "199.244.76.167/32"}, {"type": "network", "value": "199.244.76.203/32"}, {"type": "network", "value": "199.244.76.204/32"}, {"type": "network", "value": "199.244.76.164/32"}, {"type": "network", "value": "199.244.76.165/32"}, {"type": "network", "value": "199.244.76.166/32"}, {"type": "network", "value": "199.244.76.174/32"}, {"type": "network", "value": "199.244.76.173/32"}, {"type": "object", "name": "Hixney.net_2"}, {"type": "network", "value": "10.30.33.36/32"}, {"type": "network", "value": "10.30.32.54/32"}, {"type": "network", "value": "10.30.33.29/32"}, {"type": "network", "value": "72.224.107.11/32"}]}, "ProviderOrg": {"type": "service", "name": "ProviderOrg", "members": [{"type": "port", "value": "9443"}, {"type": "port", "value": "www"}, {"type": "port", "value": "https"}]}, "CITRIX_EXTERNAL": {"type": "network", "name": "CITRIX_EXTERNAL", "members": [{"type": "object", "name": "XENAPP02"}, {"type": "object", "name": "XENAPP30"}, {"type": "object", "name": "CITRIX_STOREFRONT"}, {"type": "object", "name": "P-IT-MGR"}, {"type": "object", "name": "XENAPP01"}]}, "CITRIXGATEWAY.DMZ": {"type": "network", "name": "CITRIXGATEWAY.DMZ", "members": [{"type": "object", "name": "NETSCALER.VPX"}, {"type": "object", "name": "NETSCALER.WEB"}, {"type": "object", "name": "NETSCALERSUBNETIP"}, {"type": "object", "name": "MEDITECHAPIVIP2"}, {"type": "object", "name": "NETADMIN.DMZ.TEST"}, {"type": "object", "name": "DMZ_TEST.NLH.ORG"}]}, "CITRIX_INTERNAL_TO_DMZ": {"type": "network", "name": "CITRIX_INTERNAL_TO_DMZ", "members": [{"type": "object", "name": "XENAPP02"}, {"type": "object", "name": "XENAPP30"}, {"type": "object", "name": "CITRIX_STOREFRONT"}, {"type": "object", "name": "P-IT-MGR"}, {"type": "object", "name": "NLHDC01.NLH.ORG"}, {"type": "object", "name": "NLHDC02.NLH.ORG"}, {"type": "object", "name": "p_mis_netadmin"}, {"type": "object", "name": "P_MIS_CISCOMON"}, {"type": "object", "name": "XENAPP17"}, {"type": "object", "name": "XENAPP18"}, {"type": "object", "name": "XENAPP19"}, {"type": "object", "name": "P_MIS52.WAYNE"}, {"type": "object", "name": "EUGENE10"}, {"type": "object", "name": "NLH-ISWEB.INTERNAL"}, {"type": "object", "name": "NLHTEST01"}, {"type": "object", "name": "NLHPRTG01"}, {"type": "object", "name": "XENAPP01"}, {"type": "object", "name": "LUCIUS29-iDRAC"}, {"type": "object", "name": "IT_TEST"}, {"type": "object", "name": "LUCIUS31-iDRAC"}, {"type": "object", "name": "NLHUTILITY"}]}, "MIS_TEST": {"type": "network", "name": "MIS_TEST", "members": [{"type": "object", "name": "p_mis_netadmin"}, {"type": "object", "name": "st_netadmin"}, {"type": "object", "name": "p_mis_netadmin2"}, {"type": "object", "name": "BILL_BAIRD"}, {"type": "object", "name": "<PERSON>"}, {"type": "object", "name": "BANDWIDTH_TEST"}, {"type": "host", "value": "***************"}, {"type": "object", "name": "ST_NETADMIN2"}, {"type": "object", "name": "BANDWIDTH_TEST_2"}, {"type": "host", "value": "**************"}, {"type": "object", "name": "P-ITMGR"}, {"type": "object", "name": "CISCONMON"}, {"type": "host", "value": "**************"}]}, "MAIL_VIRUS": {"type": "service", "name": "MAIL_VIRUS", "members": [{"type": "port", "value": "9780"}, {"type": "port", "value": "63595"}]}, "MARKETO_SPAMMER": {"type": "network", "name": "MARKETO_SPAMMER", "members": [{"type": "network", "value": "************/24"}, {"type": "network", "value": "************/24"}, {"type": "network", "value": "************/24"}, {"type": "network", "value": "************/24"}]}, "STAT_RAD": {"type": "service", "name": "STAT_RAD", "members": [{"type": "port", "value": "5406"}]}, "StatRadService": {"type": "service", "name": "StatRadService", "members": [{"type": "port", "value": "5406"}]}, "PAT_ACCTS_FTP": {"type": "service", "name": "PAT_ACCTS_FTP", "members": [{"type": "range", "value": "49730-49750"}, {"type": "range", "value": "49000-50000"}]}, "UDP_TEST": {"type": "service", "name": "UDP_TEST", "members": [{"type": "port", "value": "59266"}, {"type": "port", "value": "51239"}]}, "ENDOWORKS": {"type": "network", "name": "ENDOWORKS", "members": [{"type": "object", "name": "NLHENDO01"}, {"type": "object", "name": "ENDOWORKS02"}, {"type": "object", "name": "ENDOWORKS03"}, {"type": "object", "name": "NLHENDO01.NLH.ORG"}]}, "CE000SVC": {"type": "service", "name": "CE000SVC", "members": [{"type": "port", "value": "8277"}]}, "CE2000.INTERNAL": {"type": "network", "name": "CE2000.INTERNAL", "members": [{"type": "object", "name": "P_IS_PACS"}, {"type": "object", "name": "P_IS_RAMANI"}]}, "CE2000.EXTERNAL": {"type": "network", "name": "CE2000.EXTERNAL", "members": [{"type": "object", "name": "retsolinc2.com"}, {"type": "object", "name": "retsolinc3.com"}]}, "CE2000": {"type": "service", "name": "CE2000", "members": [{"type": "port", "value": "8277"}]}, "Group_24.97.36.3": {"type": "network", "name": "Group_24.97.36.3", "members": [{"type": "object", "name": "LAN"}]}, "Group_65.114.41.136": {"type": "network", "name": "Group_65.114.41.136", "members": [{"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "MDITEST"}]}, "Group_12.39.198.49": {"type": "network", "name": "Group_12.39.198.49", "members": [{"type": "object", "name": "LabCorp3"}, {"type": "object", "name": "LabCorpDev"}, {"type": "object", "name": "LabCorpProd"}]}, "Group_173.84.224.94": {"type": "network", "name": "Group_173.84.224.94", "members": [{"type": "object", "name": "pacs.net_1"}]}, "Group_12.152.123.2": {"type": "network", "name": "Group_12.152.123.2", "members": [{"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "MDITEST"}]}, "HIXNY.MBMS.INTERNAL": {"type": "network", "name": "HIXNY.MBMS.INTERNAL", "members": [{"type": "object", "name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1"}]}, "mckesson": {"type": "service", "name": "m<PERSON>sson", "members": [{"type": "range", "value": "1025-65535"}]}, "NUVODIA_VPN_NLH_PEER": {"type": "network", "name": "NUVODIA_VPN_NLH_PEER", "members": [{"type": "object", "name": "NUVODIA_VPN_NLH_PEER1"}, {"type": "object", "name": "NUVODIA_VPN_NLH_PEER2"}]}, "CLEARWATERTEST": {"type": "network", "name": "CLEARWATERTEST", "members": [{"type": "object", "name": "CLEARWATER1"}, {"type": "object", "name": "CLEARWATER2"}, {"type": "object", "name": "CLEARWATER3"}, {"type": "object", "name": "CLEARWATER4"}]}, "HIXNY.INTERNAL.GROUP": {"type": "network", "name": "HIXNY.INTERNAL.GROUP", "members": [{"type": "object", "name": "HIXNY.INTERNAL1"}, {"type": "object", "name": "HIXNY.PEER.INTERNAL.PROD"}, {"type": "object", "name": "HIXNY.PEER.INTERNAL.TEST"}, {"type": "object", "name": "HIXNY.INTERNAL.TESTHUB"}, {"type": "object", "name": "HIXNY.INTERNAL.PUSH_SERVER"}]}, "MDI.GROUP": {"type": "network", "name": "MDI.GROUP", "members": [{"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "MDITEST"}]}, "NYOH.INTERNAL.NET": {"type": "network", "name": "NYOH.INTERNAL.NET", "members": [{"type": "object", "name": "NYOH.INTERNAL.1"}, {"type": "object", "name": "NYOH.INTERNAL.3"}, {"type": "object", "name": "NYOH.INTERNAL.5"}, {"type": "object", "name": "NYOH.INTERNAL.2"}, {"type": "object", "name": "NYOH.INTERNAL.AMBRA"}, {"type": "object", "name": "NYOH.INTERNAL.CLOUD"}, {"type": "object", "name": "NYOH.INTERNAL.MEDICOM"}, {"type": "object", "name": "NYOH.INTERNAL.POWERSHARE"}]}, "NUVODIA.VPN.SENDPOINT.MASTER": {"type": "network", "name": "NUVODIA.VPN.SENDPOINT.MASTER", "members": [{"type": "object", "name": "NUVODIA.INTERNAL.NEW.1"}, {"type": "object", "name": "NUVODIA.INTERNAL.NEW.2"}, {"type": "object", "name": "NUVODIA_VPN_NLH_PEER1"}, {"type": "object", "name": "NUVODIA_VPN_NLH_PEER2"}]}, "DM_INLINE_NETWORK_7": {"type": "network", "name": "DM_INLINE_NETWORK_7", "members": [{"type": "group", "name": "MVO_Allow_OUTBOUND_Group"}, {"type": "group", "name": "NUVODIA_VPN_NLH_PEER"}]}, "DM_INLINE_NETWORK_8": {"type": "network", "name": "DM_INLINE_NETWORK_8", "members": [{"type": "object", "name": "SENTRYDS"}, {"type": "object", "name": "SENTRYDS.NET"}]}, "DM_INLINE_NETWORK_9": {"type": "network", "name": "DM_INLINE_NETWORK_9", "members": [{"type": "object", "name": "SENTRYDS"}, {"type": "object", "name": "SENTRYDS.NET"}]}, "SMHA.RAD.NEW": {"type": "network", "name": "SMHA.RAD.NEW", "members": [{"type": "object", "name": "SMHA.syn1"}, {"type": "object", "name": "SMHA.syn2"}, {"type": "object", "name": "SMHA.Synapse.Dest"}]}, "DM_INLINE_SERVICE_1": {"type": "service", "name": "DM_INLINE_SERVICE_1", "members": []}, "DM_INLINE_NETWORK_10": {"type": "network", "name": "DM_INLINE_NETWORK_10", "members": [{"type": "network", "value": "*************/24"}, {"type": "object", "name": "NLHDMZ01_SWITCH"}]}, "MEDENT.NIMBLE.OPENVPN": {"type": "network", "name": "MEDENT.NIMBLE.OPENVPN", "members": [{"type": "object", "name": "MEDENT.NIMBLE.INSIDE.1"}]}, "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP": {"type": "network", "name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "members": [{"type": "object", "name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1"}, {"type": "object", "name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2"}]}, "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP": {"type": "network", "name": "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP", "members": [{"type": "object", "name": "MEDENT.NIMBLE.INSIDE.1"}]}, "MEDENT_TELEMED": {"type": "service", "name": "MEDENT_TELEMED", "members": [{"type": "port", "value": "1443"}, {"type": "port", "value": "3478"}]}, "TCPUDP": {"type": "protocol", "name": "TCPUDP", "members": []}, "EXPANSE_VLANS": {"type": "network", "name": "EXPANSE_VLANS", "members": [{"type": "object", "name": "EXPANSE_VLAN1"}, {"type": "object", "name": "EXPANSE_VLAN2"}, {"type": "object", "name": "EXPANSE_VLAN3"}, {"type": "object", "name": "EXPANSE_VLAN4"}]}, "SmartNet_Devices": {"type": "network", "name": "SmartNet_Devices", "members": [{"type": "object", "name": "NLH_DCDS_9300s"}, {"type": "object", "name": "SR_STACK_01"}, {"type": "object", "name": "NLH.CORE.INTERNAL"}, {"type": "object", "name": "DCDS.CORE.INTERNAL"}, {"type": "object", "name": "GPC_STACK"}]}, "Domain.Controllers.Group": {"type": "network", "name": "Domain.Controllers.Group", "members": [{"type": "object", "name": "NLHDC01.NLH.ORG"}, {"type": "object", "name": "NLHDC02.NLH.ORG"}]}, "Quest.NLH2Quest.Internal": {"type": "network", "name": "Quest.NLH2Quest.Internal", "members": [{"type": "object", "name": "MAGICA"}, {"type": "object", "name": "NLI-BG04"}]}, "CHANGE.HEALTHCARE.EXTERNAL.GROUP": {"type": "network", "name": "CHANGE.HEALTHCARE.EXTERNAL.GROUP", "members": [{"type": "object", "name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD"}, {"type": "object", "name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST"}]}, "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP": {"type": "network", "name": "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP", "members": [{"type": "object", "name": "NLI.T.BG01"}]}, "CHC.EXTERNAL.NETWORK": {"type": "network", "name": "CHC.EXTERNAL.NETWORK", "members": [{"type": "object", "name": "CHC.EXTERNAL.1"}, {"type": "object", "name": "CHC.EXTERNAL.2"}, {"type": "object", "name": "CHC.EXTERNAL.3"}]}, "PHINMS.GROUP": {"type": "network", "name": "PHINMS.GROUP", "members": [{"type": "object", "name": "NLHFTP01.NLH.ORG"}, {"type": "object", "name": "NLI-T-BG01"}]}, "PHINMS": {"type": "service", "name": "PHINMS", "members": [{"type": "port", "value": "5088"}, {"type": "port", "value": "5089"}, {"type": "port", "value": "6087"}]}, "NLI.INTERNAL.NAT.CHC": {"type": "network", "name": "NLI.INTERNAL.NAT.CHC", "members": [{"type": "object", "name": "NLI-BG04.CHC.NAT"}, {"type": "object", "name": "NLI-T-BG01.CHC.NAT"}]}, "NLI-BG-GROUP": {"type": "network", "name": "NLI-BG-GROUP", "members": [{"type": "object", "name": "NLI-BG01.nlh.org"}, {"type": "object", "name": "NLI-T-BG01"}, {"type": "object", "name": "NLI-BG04"}]}, "DM_INLINE_NETWORK_11": {"type": "network", "name": "DM_INLINE_NETWORK_11", "members": [{"type": "group", "name": "EXPANSE_VLANS"}, {"type": "object", "name": "LAN"}]}, "QUEST.VPN.INTERNAL.GROUP.2": {"type": "network", "name": "QUEST.VPN.INTERNAL.GROUP.2", "members": [{"type": "object", "name": "NLI-BG04"}, {"type": "object", "name": "NLI-T-BG01"}, {"type": "host", "value": "**************3"}, {"type": "host", "value": "**************2"}, {"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "MDITEST.NLH.ORG"}]}, "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP": {"type": "network", "name": "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "members": [{"type": "object", "name": "WEBSSO.MEDITECH.COM"}, {"type": "object", "name": "WEBSSO2FA.MEDITECH.COM"}]}, "BACKLINE.LDAP.NLH.INTERNAL": {"type": "network", "name": "BACKLINE.LDAP.NLH.INTERNAL", "members": [{"type": "object", "name": "NLHDC01.NLH.ORG"}, {"type": "object", "name": "NLHDC02.NLH.ORG"}]}, "FIRECALLSYSTEM": {"type": "network", "name": "FIRECALLSYSTEM", "members": [{"type": "object", "name": "FIRECALL_JSC"}, {"type": "object", "name": "DDPC.FIREALARM"}]}, "FIRECALLSYSTEM__ENDPOINTS": {"type": "network", "name": "FIRECALLSYSTEM__ENDPOINTS", "members": [{"type": "object", "name": "FIRECALLSYSTEM_ENDPOINTS1"}, {"type": "object", "name": "FIRECALLSYSTEM_ENDPOINTS2"}]}, "SALUCRO_FTP": {"type": "service", "name": "SALUCRO_FTP", "members": [{"type": "range", "value": "20000-21000"}]}, "IT_DEPT": {"type": "network", "name": "IT_DEPT", "members": [{"type": "object", "name": "P_IT_COOR"}, {"type": "object", "name": "p_mis_netadmin"}, {"type": "object", "name": "P_IT_TECH1"}, {"type": "object", "name": "<PERSON>"}, {"type": "object", "name": "P-IT-MGR"}, {"type": "object", "name": "HIRAM"}, {"type": "object", "name": "RYAN"}, {"type": "object", "name": "NICK"}, {"type": "object", "name": "IT_TEST"}]}, "QUEST_SFTP_NEW": {"type": "service", "name": "QUEST_SFTP_NEW", "members": [{"type": "port", "value": "11022"}]}, "FULL_PORT_ACCESS": {"type": "network", "name": "FULL_PORT_ACCESS", "members": [{"type": "object", "name": "NLHAV01.NLH.ORG"}]}, "CISCO_INTERNAL_2_EXTERNAL_ACL": {"type": "network", "name": "CISCO_INTERNAL_2_EXTERNAL_ACL", "members": [{"type": "object", "name": "FIREPOWER_VM_ESXI"}, {"type": "object", "name": "CISCOPRIME.INTERNAL"}, {"type": "object", "name": "CISCOPRIMEINF"}, {"type": "object", "name": "SYSLOGSERVER"}]}, "HEALTHTOUCH.NLH.INTERNAL": {"type": "network", "name": "HEALTHTOUCH.NLH.INTERNAL", "members": [{"type": "object", "name": "HEALTHTOUCH01"}, {"type": "object", "name": "HEALTHTOUCH02"}]}, "HEALTHTOUCH.PEER.INTERNAL": {"type": "network", "name": "HEALTHTOUCH.PEER.INTERNAL", "members": [{"type": "object", "name": "HEALTHTOUCH.PEER.INTERNAL.2"}, {"type": "object", "name": "HEALTHTOUCH.PEER.INTERNAL.1"}]}, "Greycastle_Testing_External": {"type": "network", "name": "Greycastle_Testing_External", "members": [{"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}]}, "LINKBG.SPAM.GROUP": {"type": "network", "name": "LINKBG.SPAM.GROUP", "members": [{"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}]}, "love.explorethebest.com.spam.group": {"type": "network", "name": "love.explorethebest.com.spam.group", "members": [{"type": "object", "name": "love.explorethebest.com.spam.1"}, {"type": "object", "name": "love.explorethebest.com.spam.2"}, {"type": "object", "name": "love.explorethebest.com.spam.3"}]}, "NLH.ACRONIS.GROUP.INSIDE": {"type": "network", "name": "NLH.ACRONIS.GROUP.INSIDE", "members": [{"type": "object", "name": "HARRIET.NLH.ORG"}, {"type": "object", "name": "LUCIUS32.NLH.ORG"}, {"type": "object", "name": "NLHEXCHANGE.NLH.ORG"}, {"type": "object", "name": "LUCIUS10A.NLH.ORG"}, {"type": "object", "name": "LUCIUS19B.NLH.ORG"}, {"type": "object", "name": "WILLYWONKA.NLH.ORG"}, {"type": "object", "name": "NLHSYN01.NLH.ORG"}, {"type": "object", "name": "NLHSYN02.NLH.ORG"}, {"type": "object", "name": "NLHSYN03.NLH.ORG"}, {"type": "object", "name": "NLHSYN04.NLH.ORG"}, {"type": "object", "name": "NLHSP19WEB.NLH.ORG"}, {"type": "object", "name": "NLHSP19APP.NLH.ORG"}, {"type": "object", "name": "NLHSP19OFCWEB.NLH.ORG"}, {"type": "object", "name": "LUCIUS18C.NLH.ORG"}, {"type": "object", "name": "LUCIUS19C.NLH.ORG"}, {"type": "object", "name": "LUCIUS14.NLH.ORG"}, {"type": "object", "name": "LUCIUS26D.NLH.ORG"}, {"type": "object", "name": "LUCUIS16B.NLH.ORG"}, {"type": "object", "name": "ARCHIVE.NLH.ORG"}, {"type": "object", "name": "DR1.NLH.ORG"}, {"type": "object", "name": "FAXSERVER.NLH.ORG"}, {"type": "object", "name": "LUCIUS17B.NLH.ORG"}, {"type": "object", "name": "LUCIUS19A.NLH.ORG"}, {"type": "object", "name": "LUCIUS25A.NLH.ORG"}, {"type": "object", "name": "LUCIUS26B.NLH.ORG"}, {"type": "object", "name": "MDILIVE.NLH.ORG"}, {"type": "object", "name": "NLHBACKUP02.NLH.ORG"}, {"type": "object", "name": "NLHFTP01.NLH.ORG"}, {"type": "object", "name": "ONEVIEW.NLH.ORG"}, {"type": "object", "name": "SUMMIT.NLH.ORG"}, {"type": "object", "name": "KRONOSNEW.NLH.ORG"}, {"type": "object", "name": "NLHENDO01.NLH.ORG"}, {"type": "object", "name": "SQL01.NLH.ORG"}, {"type": "object", "name": "NLHDC01.NLH.ORG"}, {"type": "object", "name": "NLHDC02.NLH.ORG"}, {"type": "object", "name": "LUCIUS18D.NLH.ORG"}, {"type": "object", "name": "STREAMTASK.NLH.ORG"}, {"type": "object", "name": "DESIGO.NLH.ORG"}, {"type": "object", "name": "LUCIUS16A.NLH.ORG"}, {"type": "object", "name": "LUCIUS25C.NLH.ORG"}, {"type": "object", "name": "NLHMUSE01.NLH.ORG"}, {"type": "object", "name": "NLHMUSE02.NLH.ORG"}, {"type": "object", "name": "NLHPROVMDORACLE.NLH.ORG"}, {"type": "object", "name": "PROVMDAPP.NLH.ORG"}, {"type": "object", "name": "LUCIOUS01"}, {"type": "object", "name": "NLHPRTGPROBE04"}, {"type": "object", "name": "LUCIUS10C"}, {"type": "object", "name": "LUCIUS28"}, {"type": "object", "name": "COBAS.NLH.ORG"}, {"type": "object", "name": "ESICALLACCT26A.NLH.ORG"}, {"type": "object", "name": "ESRS.NLH.ORG"}, {"type": "object", "name": "LUCIUS24A.NLH.ORG"}, {"type": "object", "name": "LUCIUS24B.NLH.ORG"}, {"type": "object", "name": "LUCIUS24C.NLH.ORG"}, {"type": "object", "name": "LUCIUS24D.NLH.ORG"}, {"type": "object", "name": "LUCIUS26A.NLH.ORG"}, {"type": "object", "name": "LUCIUS26C.NLH.ORG"}, {"type": "object", "name": "LUCIUS28.NLH.ORG"}, {"type": "object", "name": "LUCIUS30.NLH.ORG"}, {"type": "object", "name": "MUSE-APP.NLH.ORG"}, {"type": "object", "name": "MUSE-NXWEB.NLH.ORG"}, {"type": "object", "name": "NLH-iUV.NLH.ORG"}, {"type": "object", "name": "NLHADMINCENTER.NLH.ORG"}, {"type": "object", "name": "NLHDRFIRST.NLH.ORG"}, {"type": "object", "name": "NLHELOCK.NLH.ORG"}, {"type": "object", "name": "NURSECALLHD.NLH.ORG"}, {"type": "object", "name": "PRADEV.NLH.ORG"}]}, "NLH.ACRONIS.GROUP.EXTERNAL": {"type": "network", "name": "NLH.ACRONIS.GROUP.EXTERNAL", "members": [{"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "object", "name": "ACRONIS.EXTERNAL.RANGE1"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "object", "name": "ACRONIS.EXTERNAL.RANGE3"}, {"type": "object", "name": "ACRONIS.EXTERNAL.RANGE4"}, {"type": "object", "name": "ACRONIS.EXTERNAL.RANGE2"}, {"type": "host", "value": "**************3"}]}, "BARRACUDA.LDAP.EXTERNAL": {"type": "network", "name": "BARRACUDA.LDAP.EXTERNAL", "members": [{"type": "object", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.1"}, {"type": "object", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.2"}, {"type": "object", "name": "BARRACUDA.LDAP.EXTERNAL.PEER.3"}]}, "REYHEALTH.EXTERNAL.GROUP": {"type": "network", "name": "REYHEALTH.EXTERNAL.GROUP", "members": [{"type": "object", "name": "REYHEALTH.EXTERNAL.EXTERNAL.1"}, {"type": "object", "name": "REYHEALTH.EXTERNAL.EXTERNAL.2"}]}, "REYHEALTH.EXTERNAL.PORT.GROUP": {"type": "service", "name": "REYHEALTH.EXTERNAL.PORT.GROUP", "members": []}, "EMAIL.BLACKLIST.EXTERNAL": {"type": "network", "name": "EMAIL.BLACKLIST.EXTERNAL", "members": [{"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "object", "name": "JELMENDORFSPAM"}]}, "DM_INLINE_SERVICE_2": {"type": "service", "name": "DM_INLINE_SERVICE_2", "members": []}, "NLH.DI.GEDEVICES": {"type": "network", "name": "NLH.DI.GEDEVICES", "members": [{"type": "object", "name": "DI.AWSERVER"}, {"type": "object", "name": "DI.AWSERVER.ILO"}, {"type": "object", "name": "DI.CT.ADV.WS"}, {"type": "object", "name": "DI.CTSCANNER"}, {"type": "object", "name": "DI.GE.MAMMO.INTERFACE"}, {"type": "object", "name": "DI.MAMMO"}, {"type": "object", "name": "DI.MAMMO.SHUTTLE"}, {"type": "object", "name": "DI.MRI.ALLIANCE"}, {"type": "object", "name": "DI.MUSE01"}, {"type": "object", "name": "DI.MUSE02"}, {"type": "object", "name": "DI.MUSE03"}, {"type": "object", "name": "DI.NUCMEDCAMERA"}, {"type": "object", "name": "DI.PERTH.XRAY"}, {"type": "object", "name": "DI.PETCTVIEWER"}, {"type": "object", "name": "DI.R.AND.F"}, {"type": "object", "name": "DI.ROOMA"}, {"type": "object", "name": "DI.XELERIS.NM"}]}, "DM_INLINE_NETWORK_3": {"type": "network", "name": "DM_INLINE_NETWORK_3", "members": [{"type": "object", "name": "NLI-BG04.CHC.NAT"}, {"type": "object", "name": "NLI-T-BG01.CHC.NAT"}]}, "blackblazeb2.goup": {"type": "network", "name": "blackblazeb2.goup", "members": [{"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}]}, "HAYNS.EXTERNAL.GROUP": {"type": "network", "name": "HAYNS.EXTERNAL.GROUP", "members": [{"type": "host", "value": "***************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "**************"}, {"type": "object", "name": "HANYS.EXTERNAL.1"}, {"type": "object", "name": "HANYS.EXTERNAL.3"}, {"type": "object", "name": "HANYS.INTERNAL.2"}]}, "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP": {"type": "network", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP", "members": [{"type": "object", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1"}, {"type": "object", "name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2"}]}, "Incident.External": {"type": "network", "name": "Incident.External", "members": [{"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}]}, "NLH.Firewall.Internal.Group": {"type": "network", "name": "NLH.Firewall.Internal.Group", "members": [{"type": "object", "name": "ASA01"}, {"type": "object", "name": "ASA02"}]}, "Clearwater.Internal.Group": {"type": "network", "name": "Clearwater.Internal.Group", "members": [{"type": "object", "name": "Clearwater.Internal.Peer.Range"}]}, "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP": {"type": "network", "name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "members": [{"type": "object", "name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1"}, {"type": "object", "name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2"}]}, "SSI.EXTERNAL.PEER.GROUP": {"type": "network", "name": "SSI.EXTERNAL.PEER.GROUP", "members": [{"type": "object", "name": "SSI.EXTERNAL.PEER.1"}]}, "BANDWIDTH.TEST.GROUP.OUTSIDE": {"type": "network", "name": "BANDWIDTH.TEST.GROUP.OUTSIDE", "members": [{"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***************"}, {"type": "network", "value": "*************/24"}, {"type": "network", "value": "**********/24"}]}, "FRESHWORKS.EXCLUSIONS.GROUP": {"type": "network", "name": "FRESHWORKS.EXCLUSIONS.GROUP", "members": [{"type": "object", "name": "FRESHWORKS.EXCLUSIONS.1"}, {"type": "object", "name": "FRESHWORKS.EXCLUSIONS.2"}, {"type": "object", "name": "FRESHWORKS.EXCLUSIONS.3"}, {"type": "object", "name": "FRESHWORKS.EXCLUSIONS.5"}, {"type": "object", "name": "FRESHWORKS.EXCLUSIONS.6"}, {"type": "object", "name": "FRESHWORKS.EXCLUSIONS.7"}]}}, "service_objects": {"obj-tcp-eq-80": {"name": "obj-tcp-eq-80", "protocol": "TCP", "port": "www"}, "obj-tcp-eq-15002": {"name": "obj-tcp-eq-15002", "protocol": "TCP", "port": "15002"}, "obj-tcp-eq-15331": {"name": "obj-tcp-eq-15331", "protocol": "TCP", "port": "15331"}, "obj-tcp-eq-3389": {"name": "obj-tcp-eq-3389", "protocol": "TCP", "port": "3389"}, "obj-tcp-eq-2222": {"name": "obj-tcp-eq-2222", "protocol": "TCP", "port": "2222"}, "obj-tcp-eq-6544": {"name": "obj-tcp-eq-6544", "protocol": "TCP", "port": "6544"}, "obj-tcp-eq-2020": {"name": "obj-tcp-eq-2020", "protocol": "TCP", "port": "2020"}, "obj-tcp-eq-23": {"name": "obj-tcp-eq-23", "protocol": "TCP", "port": "telnet"}, "obj-tcp-eq-15031": {"name": "obj-tcp-eq-15031", "protocol": "TCP", "port": "15031"}, "obj-tcp-eq-5631": {"name": "obj-tcp-eq-5631", "protocol": "TCP", "port": "pcanywhere-data"}, "obj-udp-eq-15032": {"name": "obj-udp-eq-15032", "protocol": "UDP", "port": "15032"}, "obj-udp-eq-5632": {"name": "obj-udp-eq-5632", "protocol": "UDP", "port": "pcanywhere-status"}, "obj-tcp-eq-25": {"name": "obj-tcp-eq-25", "protocol": "TCP", "port": "smtp"}, "obj-tcp-eq-443": {"name": "obj-tcp-eq-443", "protocol": "TCP", "port": "https"}, "obj-tcp-eq-55443": {"name": "obj-tcp-eq-55443", "protocol": "TCP", "port": "55443"}, "obj-tcp-eq-3401": {"name": "obj-tcp-eq-3401", "protocol": "TCP", "port": "3401"}, "obj-tcp-eq-53048": {"name": "obj-tcp-eq-53048", "protocol": "TCP", "port": "53048"}, "obj-tcp-eq-53372": {"name": "obj-tcp-eq-53372", "protocol": "TCP", "port": "53372"}, "obj-tcp-eq-53050": {"name": "obj-tcp-eq-53050", "protocol": "TCP", "port": "53050"}, "obj-tcp-eq-53374": {"name": "obj-tcp-eq-53374", "protocol": "TCP", "port": "53374"}, "obj-tcp-eq-21": {"name": "obj-tcp-eq-21", "protocol": "TCP", "port": "ftp"}, "NLI-BG13-FTP": {"name": "NLI-BG13-FTP", "protocol": "TCP", "port": "1433"}, "W32.MYDOOM.OLD": {"name": "W32.MYDOOM.OLD", "protocol": "UDP", "port": "3127"}, "GREYCASTLE_VPN": {"name": "GREYCASTLE_VPN", "protocol": "UDP", "port": "51820"}, "IMO_CLOUD": {"name": "IMO_CLOUD", "protocol": "TCP", "port": "42045"}, "NOVA-8070-TCP": {"name": "NOVA-8070-TCP", "protocol": "TCP", "port": "8070"}, "REYHEALTH.EXTERNAL.PORT1": {"name": "REYHEALTH.EXTERNAL.PORT1", "protocol": "TCP", "port": "18009"}, "REYHEALTH.EXTERNAL.PORT2": {"name": "REYHEALTH.EXTERNAL.PORT2", "protocol": "TCP", "port": "18005"}, "NOVA.TOPAZ": {"name": "NOVA.TOPAZ", "protocol": "TCP", "port": "47290"}}, "access_lists": {"inside_access_in": [{"line": "access-list inside_access_in extended deny ip any object-group Incident.External", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended deny ip any object MICROSOFTSTREAM.COM", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended deny ip any object-group BANDWIDTH.TEST.GROUP.OUTSIDE inactive", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended deny ip ************* ************* object GUEST_NETWORK", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in remark Allowed TCP Traffic", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in remark Allowed TCP Traffic", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 object-group in_any_to_out_any_tcp", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit tcp any4 any4 object-group IMO_Ports", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip interface inside object Clearwater.Internal.Peer.Range", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in remark Allowed UDP Traffic", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit udp any4 any4 object-group any_in_udp_to_any_out", "action": "permit", "protocol": "udp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit icmp any4 any4", "action": "permit", "protocol": "icmp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit object-group REYHEALTH.EXTERNAL.PORT.GROUP any object-group REYHEALTH.EXTERNAL.GROUP", "action": "permit", "protocol": "object-group", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any object-group FRESHWORKS.EXCLUSIONS.GROUP", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group CISCO_INTERNAL_2_EXTERNAL_ACL any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group MIS_TEST any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group NLH.ACRONIS.GROUP.INSIDE object-group NLH.ACRONIS.GROUP.EXTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object CISCO.WSA.INTERNAL any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group Domain.Controllers.Group any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group NLH.DI.GEDEVICES object GESUPPORT.INTERNAL.NET", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object DI.NET any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object pacs.net_1 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit object-group TCPUDP any any eq domain", "action": "permit", "protocol": "object-group", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any4 object-group QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit tcp object-group ExchangeServers any4 eq smtp", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object EXPANSE_VLAN1 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object EXPANSE_VLAN2 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object EXPANSE_VLAN3 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object EXPANSE_VLAN4 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLHBRAUNPUMPS.INTERNAL object NLHBRAUNPUMPS.EXTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLI-BG04 any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLHSSI any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLHSSI object-group SSI.EXTERNAL.PEER.GROUP", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object MDILIVE.NLH.ORG object PATIENT_PORTAL_1", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLHPRTG01 object-group DM_INLINE_NETWORK_10", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group NUVODIA.VPN.SENDPOINT.MASTER any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object NLHKIWISYSLOG01.NLH.ORG any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip any4 object-group NYOH.INTERNAL.NET", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group NUVODIA.INTERNAL.GROUP.NEW object NYOH.INTERNAL.5 inactive", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object NYOH.INTERNAL.5 object-group NUVODIA.INTERNAL.GROUP.NEW inactive", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group NUVODIA.INTERNAL.PEER.NET1 any4 inactive", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group FULL_PORT_ACCESS any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group IT_DEPT object-group CITRIXGATEWAY.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group IT_DEPT object DMZ_TEST.NLH.ORG", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group SmartNet_Devices any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object MAGICA object questlab", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group FIRECALLSYSTEM object-group FIRECALLSYSTEM__ENDPOINTS", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in remark Rules for Citrix Remote Access", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group CITRIX_INTERNAL_TO_DMZ object-group CITRIXGATEWAY.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group MDI_Group any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object MEDENT host ************", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group MEDENT_GROUP any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP object-group MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in remark For Pharmacy Per Ramani Port 8277", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp object-group CE2000.INTERNAL object-group CE2000.EXTERNAL object-group CE2000", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in remark Just leaving a note here, does this server need all TCP ports open", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp object HYPER-V_CLUSTER any4", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object PDX.Internal any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object RCARE-SERVER any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object ROBOT_GE_VOT_TRAIN any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit object NLI-BG13-FTP object NLI-BG01.nlh.org any", "action": "permit", "protocol": "object", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object NLHUTILITY any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group HEALTHTOUCH.NLH.INTERNAL object-group HEALTHTOUCH.PEER.INTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group Medivators any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object NOVA-QIE.INTERNAL any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq 1777", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq 44315", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq 10000", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq 8200", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 range 6101 6102", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 object-group CoreFTP", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq klogin", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in remark Allow iPhones to mail.mac.com to stop constant retries", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp any4 any4 eq 993", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip any4 object-group WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any4 object-group HIXNY", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any4 object GEserviceNET", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip any4 object-group SMHA.RAD", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip any4 object-group medinotes", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in remark Whitelisting for Direct Messaging", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp object MAGICA object-group ProviderOrg.External object-group ProviderOrg", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object-group FoodService object Sodexho", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit tcp object-group ProvationServers object Provation-out eq smtp", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object IMO_2 any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp object MCKESSON.MC.PHARM object newsync3.mkesson.com object-group mckesson", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in extended permit ip object ESRS_EMC_VIRTUAL_APPLIANCE any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit tcp object-group PHINMS.GROUP any object-group PHINMS", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_access_in remark This is for traffic for the CHC/OPTUM VPN", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_access_in extended permit ip object-group NLI.INTERNAL.NAT.CHC any", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}], "outside_access_in": [{"line": "access-list outside_access_in extended permit icmp any4 any4", "action": "permit", "protocol": "icmp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object-group BANDWIDTH.TEST.GROUP.OUTSIDE any inactive", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended deny ip object-group Incident.External any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended deny ip host *************** any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip host *************** any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip host ************* any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip host ************ any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip host *************** any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object backblazeb2.com any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object-group blackblazeb2.goup any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip object GESUPPORT.INTERNAL.NET any4", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object-group EMAIL.BLACKLIST.EXTERNAL any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in remark MARKETO mktomail.com Spammer", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object-group MARKETO_SPAMMER any4", "action": "deny", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended deny ip object sendgrid.net.virus any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended deny ip object-group love.explorethebest.com.spam.group any", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended deny ip object-group LINKBG.SPAM.GROUP any4", "action": "deny", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip object NYOH.INTERNAL.5 any4 inactive", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip object CHC.OPTUM.NAT.INTERNAL.SUB object-group NLI.INTERNAL.NAT.CHC", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip object-group HEALTHTOUCH.PEER.INTERNAL object-group HEALTHTOUCH.NLH.INTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip object NOVA.INTERLACE.PEER.EXTERNAL2 object NOVA-QIE.INTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip object NOVA.INTERLACE.PEER.EXTERNAL object NOVA-QIE.INTERNAL", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object NOVA-QIE.INTERNAL inactive", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit object-group DM_INLINE_SERVICE_2 object BARRACUDA.CLOUD.EXTERNAL object NLHEXCHANGE.NLH.ORG", "action": "permit", "protocol": "object-group", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip object-group CLEARWATERTEST object NLHEXCHANGE.NLH.ORG inactive", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit tcp any object NLHEXCHANGE.NLH.ORG eq https", "action": "permit", "protocol": "tcp", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip object Clearwater.Internal.Peer.Range interface inside", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object BARRACUDA.EMAIL.INSIDE", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any4 object PATIENTPORTAL.EXTERNAL", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object PATIENTPORTAL.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object mtrestexpapis-test01.nlh.org.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object mtrestexpapis-live01.nlh.org.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object mtrestexpapis-live01.nlh.org.external", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object mtrestexpapis-test01.nlh.org.external", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG object-group PaceGlobalgrp", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15031", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit udp any4 object DIRECT.NLH.ORG eq 15032", "action": "permit", "protocol": "udp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15002", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15331", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any4 object DIRECT.NLH.ORG", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any4 object-group NLI-BG-GROUP", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip any object NETSCALER.WEB", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object DUOTEST.NLH.ORG", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object DUOTEST.NLH.ORG.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit tcp any4 object NLH-ISWEB.DMZ eq https", "action": "permit", "protocol": "tcp", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any object NLH-ISWEB.DMZVR", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip any4 object NLH-WEB01-WS01", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip object-group ProviderOrg.External object DIRECT.NLH.ORG", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list outside_access_in extended permit ip any object MAGICA", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in extended permit ip object BRIAN_DHCP object NLHTESTMOBILE", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list outside_access_in remark Rule for Castle DR Implementation", "action": "deny", "protocol": "any", "source": "any", "destination": "any", "service": "any"}], "DMZ_access_in_V1": [{"line": "access-list DMZ_access_in_V1 extended permit ip ************* ************* object NLHPRTG01", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list DMZ_access_in_V1 extended permit ip object-group CITRIXGATEWAY.DMZ object-group IT_DEPT", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list DMZ_access_in_V1 extended permit ip object DMZ_TEST.NLH.ORG object-group IT_DEPT", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list DMZ_access_in_V1 extended permit ip object-group CITRIXGATEWAY.DMZ object-group CITRIX_INTERNAL_TO_DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list DMZ_access_in_V1 extended deny ip ************* ************* object LAN", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list DMZ_access_in_V1 extended deny ip ************* ************* object-group EXPANSE_VLANS", "action": "deny", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}], "inside_nat0_outbound": [{"line": "access-list inside_nat0_outbound extended permit ip object MAGICA object questlab", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip any4 *************92 ***************", "action": "permit", "protocol": "ip", "source": "any4", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object LAN *************92 ***************", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object pacs.net_1 object PhilipsSupport", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object-group MVO_Allow_OUTBOUND_Group object MVOrtho.net", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorp3", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorpDev", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorpProd", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object MilleniumPACSnat object-group MilleniumPACS", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object-group RAD.PACS.READ object-group SMHA.RAD", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object pacs.net_1 object MVOatJSC.net", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object-group MVO_Allow_OUTBOUND_Group object pacs.net_1", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object-group FoodSVC object-group Healthtouch.out", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object LAN object MVOrtho.net", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any"}, {"line": "access-list inside_nat0_outbound extended permit ip object MDILIVE.NLH.ORG object-group Schumacher.Inside", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object MDITEST object-group Schumacher.Inside", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object PACS_NEW object-group AAI.NYOH.PACS", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object-group CITRIX_INTERNAL_TO_DMZ object-group CITRIXGATEWAY.DMZ", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}, {"line": "access-list inside_nat0_outbound extended permit ip object INTERLACE object-group SMHA.RAD", "action": "permit", "protocol": "ip", "source": "any", "destination": "any", "service": "any", "has_object_groups": true}], "VPN2.nlh.org_splitTunnelAcl": [{"line": "access-list VPN2.nlh.org_splitTunnelAcl standard permit any4", "action": "permit", "protocol": "any", "source": "any", "destination": "any", "service": "any"}]}, "interface_configs": {"GigabitEthernet0/0": {"name": "GigabitEthernet0/0"}, "GigabitEthernet0/1": {"name": "GigabitEthernet0/1"}, "GigabitEthernet0/2": {"name": "GigabitEthernet0/2"}, "GigabitEthernet0/3": {"name": "GigabitEthernet0/3"}, "GigabitEthernet0/4": {"name": "GigabitEthernet0/4"}, "GigabitEthernet0/5": {"name": "GigabitEthernet0/5"}, "GigabitEthernet0/6": {"name": "GigabitEthernet0/6"}, "GigabitEthernet0/7": {"name": "GigabitEthernet0/7"}, "Management0/0": {"name": "Management0/0"}}, "routes_count": 51, "nat_rules_count": 80}}